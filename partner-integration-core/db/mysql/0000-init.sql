-- Deploy mysql:0000-init to mysql

BEGIN;

CREATE TABLE `workflow_execution`
(
    `id`            BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `workflow_id`   VARCHAR(36)     NOT NULL DEFAULT ''                        COMMENT 'Identifier of the workflow',
    `run_id`        CHAR(36)        NOT NULL DEFAULT ''                        COMMENT 'ID per workflow execution',
    `transition_id` CHAR(36)        NOT NULL DEFAULT ''                        COMMENT 'ID of current state transition',
    `prev_trans_id` CHAR(36)                 DEFAULT ''                        COMMENT 'ID of previous state transition',
    `attempt`       INT             NOT NULL DEFAULT 0                         COMMENT 'Attempt count per transition',
    `state`         INT             NOT NULL                                   COMMENT 'State of the workflow',
    `data`          JSON            NOT NULL                                   COMMENT 'Data required for running workflow',
    `created_at`    DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`    DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON>AR<PERSON>EY (`id`,`updated_at`),
    UNIQUE KEY `uk_run_id_workflow_id` (`run_id`,`workflow_id`,`updated_at`) COMMENT 'Identifier for a specific execution',
    UNIQUE KEY `uk_transition_id` (`transition_id`,`updated_at`) COMMENT 'Identifier for a transition within an execution',
    UNIQUE KEY `uk_prev_trans_id` (`prev_trans_id`,`updated_at`) COMMENT 'Identifier for continuing an existing execution',
    KEY             `index_state_updated_at` (`workflow_id`,`state`,`updated_at`) COMMENT 'For worker to retry failed transitions',
    KEY             `index_created_at` (`created_at`),
    KEY             `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
 PARTITION BY RANGE (((year(`updated_at`) * 100) + month(`updated_at`)))
(
    PARTITION p202509 VALUES LESS THAN (202510) ENGINE = InnoDB,
    PARTITION p202510 VALUES LESS THAN (202511) ENGINE = InnoDB,
    PARTITION p202511 VALUES LESS THAN (202512) ENGINE = InnoDB,
    PARTITION p202512 VALUES LESS THAN (202601) ENGINE = InnoDB,
    PARTITION p202601 VALUES LESS THAN (202602) ENGINE = InnoDB,
    PARTITION p202602 VALUES LESS THAN (202603) ENGINE = InnoDB,
    PARTITION p202603 VALUES LESS THAN (202604) ENGINE = InnoDB,
    PARTITION p202604 VALUES LESS THAN (202605) ENGINE = InnoDB,
    PARTITION p202605 VALUES LESS THAN (202606) ENGINE = InnoDB,
    PARTITION p202606 VALUES LESS THAN (202607) ENGINE = InnoDB,
    PARTITION p202607 VALUES LESS THAN (202608) ENGINE = InnoDB,
    PARTITION p_max VALUES LESS THAN MAXVALUE ENGINE = InnoDB
)
