-- Deploy partner-integration-api:account to mysql

BEGIN;

-- XXX Add DDLs here.

CREATE TABLE `account`
(
    id                   BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    account_id           varchar(36) NOT NULL,
    parent_account_id    varchar(36)          DEFAULT '',
    cif                  varchar(36) NOT NULL DEFAULT '',
    safe_id              varchar(36) NOT NULL DEFAULT '',
    product_variant_code varchar(36) NOT NULL DEFAULT '',
    partner_id           varchar(36) NOT NULL DEFAULT '',
    metadata             json                 DEFAULT (json_object()),
    created_at           datetime(6)    NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    created_by           varchar(32) NOT NULL DEFAULT '',
    updated_at           datetime(6)    NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    updated_by           varchar(32) NOT NULL DEFAULT '',
    PRIMAR<PERSON>EY (`id`),
    <PERSON><PERSON><PERSON>                  `cif` (`cif`),
    KEY                  `safe_id` (`safe_id`),
    UNIQUE KEY `uk_account_id` (`account_id`),
    KEY                  `index_parent_account_id` (`parent_account_id`),
    KEY                  `index_created_at` (`created_at`),
    KEY                  `index_updated_at` (`updated_at`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;