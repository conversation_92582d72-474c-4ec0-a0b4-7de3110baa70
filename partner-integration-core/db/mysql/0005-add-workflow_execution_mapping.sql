BEGIN;

CREATE TABLE `workflow_execution_mapping`
(
    `id`                               BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `run_id`                           CHAR(36)  NOT NULL COMMENT 'from workflow_execution table',
    `domain`                           CHAR(36)  NOT NULL COMMENT 'to explain what domain_id column is',
    `domain_id`                        CHAR(36)  NOT NULL COMMENT 'a foreign id to tie run_id to',
    `created_at`                       DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP (6),
    `updated_at`                       DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP (6) ON UPDATE CURRENT_TIMESTAMP (6),
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_run_id` (`run_id`),
    KEY                                `index_domain_domain_id` (`domain`, `domain_id`),
    KEY                                `index_created_at` (`created_at`),
    <PERSON><PERSON>Y                                `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4
COLLATE = utf8mb4_unicode_ci;

COMMIT;
