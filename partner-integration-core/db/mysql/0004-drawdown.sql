BEGIN;

CREATE TABLE `drawdown`
(
    `id`                               BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `partner_id`                       VARCHAR(36)  NOT NULL COMMENT 'Partner ID',
    `safe_id`                          VARCHAR(36)  NOT NULL COMMENT 'Customer safe id who initiates the drawdown',
    `reference_id`                     VARCHAR(36)  NOT NULL COMMENT 'Identifier passed from upstream to ensure handle idempotency at request level',
    `lending_transaction_id`           VARCHAR(64)  NOT NULL DEFAULT '' COMMENT 'transaction ID from lending',
    `loan_account_id`                  VARCHAR(36)  NOT NULL DEFAULT '' COMMENT 'LOC accountID',
    `destination_account`              JSON         NOT NULL COMMENT 'destination account',
    `principal_amount`                 BIGINT       NOT NULL COMMENT 'requested principal amount',
    `currency`                         CHAR(3)      NOT NULL DEFAULT '' COMMENT 'Currency of this transaction',
    `transaction_domain`               VARCHAR(36)  NOT NULL COMMENT 'transaction domain',
    `transaction_type`                 VARCHAR(36)  NOT NULL COMMENT 'transaction type',
    `transaction_subtype`              VARCHAR(36)  NOT NULL COMMENT 'transaction subtype',
    `status`                           VARCHAR(36)  NOT NULL DEFAULT '' COMMENT 'Drawdown status',
    `status_reason`                    VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Drawdown status reason',
    `status_reason_description`        VARCHAR(500) NOT NULL DEFAULT '' COMMENT 'Drawdown status reason description',
    `loan_name`                        VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'loan name',
    `preferred_repayment_day_of_month` INT              NULL COMMENT 'preferred repayment day of month',
    `loan_tenor_in_months`             INT          NOT NULL COMMENT 'loan tenor in months',
    `metadata`                         JSON COMMENT 'Additional non-essential data',
    `created_at`                       DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP (6),
    `updated_at`                       DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP (6) ON UPDATE CURRENT_TIMESTAMP (6),
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_reference_id` (`reference_id`),
    UNIQUE KEY `uk_lending_transaction_id` (`lending_transaction_id`),
    KEY                                `index_safe_id` (`safe_id`),
    KEY                                `index_loan_account_id` (`loan_account_id`),
    KEY                                `index_created_at` (`created_at`),
    KEY                                `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4
COLLATE = utf8mb4_unicode_ci;

COMMIT;
