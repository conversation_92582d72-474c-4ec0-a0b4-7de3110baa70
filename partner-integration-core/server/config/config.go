package config

import (
	v2 "gitlab.com/gx-regional/dakota/servus/v2"
)

// AppConfig ...
type AppConfig struct {
	v2.DefaultAppConfig
}

type BankInfo struct {
	SwiftCode string `json:"swiftCode"`
}

// WorkflowRetryConfig defines the different retry options
type WorkflowRetryConfig struct {
	Drawdown                  *RetryOptions `json:"drawdown"`
	LoanApplicationSubmission *RetryOptions `json:"loanApplicationSubmission"`
}

// RetryOptions defines different types of retry policies
type RetryOptions struct {
	TransactionalRetryOption *RetryPolicy `json:"transactional"`
}

// RetryPolicy defines the configs for each retry policy
type RetryPolicy struct {
	IntervalInSeconds      int `json:"intervalInSeconds"`
	MaxAttempt             int `json:"maxAttempt"`
	MaxTimeWindowInSeconds int `json:"maxTimeWindowInSeconds"`
}
