package server

import (
	v2 "gitlab.com/gx-regional/dakota/servus/v2"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/logic/lending/applicationSubmission"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/logic/lending/loan/drawdown"
)

func RegisterWorkflow(app *v2.Application) {
	drawdownWorkflow := &drawdown.WorkflowImpl{}
	app.MustRegister("workflow.drawdown", drawdownWorkflow)
	app.MustRegister("logic.drawdown", drawdown.NewDrawdownLogic)
	drawdownWorkflow.RegisterWorkflow()

	applicationSubmissionWorkflow := &applicationSubmission.WorkflowImpl{}
	app.MustRegister("workflow.applicationSubmission", applicationSubmissionWorkflow)
	applicationSubmissionWorkflow.RegisterWorkflow()
}
