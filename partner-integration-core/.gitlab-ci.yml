include:
  - project: gx-regional/dbmy/ci
    ref: master
    file: pipelines/go/library.yml

variables:
  DISABLE_VENDOR_CHECK: 'true'
  REPO_SPECIFIC_IGNORE_PATHS: /mock_|storage/|.*routes\.go
#  DISABLE_UNIT_TEST: 'true'
  MODULE_PATH: partner-integration-core
  UNITTEST_MIN_COVERAGE: 50
  SAST_EXCLUDED_ANALYZERS: "nodejs-scan"
  RELEASE_BRANCH: 'main'
  KUBERNETES_MEMORY_REQUEST: '3Gi'
  KUBERNETES_MEMORY_LIMIT: '3Gi'