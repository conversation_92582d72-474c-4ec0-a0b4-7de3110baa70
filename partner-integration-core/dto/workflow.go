package dto

import "gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage"

// WorkflowRequest is the adapter between all Workflow execution data and API layer request
type WorkflowRequest struct {
	ProfileID string
	SafeID    string
	Drawdown  *storage.Drawdown
}

// WorkflowResponse is the adapter between all Workflow execution data and API layer response
type WorkflowResponse struct {
	Status                 string
	InternalIdempotencyKey string
	Payload                interface{}
}
