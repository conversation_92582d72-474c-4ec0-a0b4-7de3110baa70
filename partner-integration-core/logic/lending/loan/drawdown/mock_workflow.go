// Code generated by mockery v2.53.3. DO NOT EDIT.

package drawdown

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	dto "gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/dto"
)

// MockWorkflow is an autogenerated mock type for the Workflow type
type MockWorkflow struct {
	mock.Mock
}

// CreateWorkflow provides a mock function with given fields: _a0, _a1
func (_m *MockWorkflow) CreateWorkflow(_a0 context.Context, _a1 *dto.WorkflowRequest) (*dto.WorkflowResponse, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for CreateWorkflow")
	}

	var r0 *dto.WorkflowResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *dto.WorkflowRequest) (*dto.WorkflowResponse, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *dto.WorkflowRequest) *dto.WorkflowResponse); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.WorkflowResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *dto.WorkflowRequest) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockWorkflow creates a new instance of MockWorkflow. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockWorkflow(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockWorkflow {
	mock := &MockWorkflow{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
