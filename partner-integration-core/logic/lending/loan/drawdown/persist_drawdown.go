package drawdown

import (
	"context"
	"errors"

	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dakota/workflowengine"
)

func (w *WorkflowImpl) persistDrawdown(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}

	nextCtx := currCtx.Clone()
	request := nextCtx.Drawdown

	err := w.DrawdownDAO.Save(ctx, &request)
	if err != nil {
		slog.FromContext(ctx).Warn(logDrawdown, "DrawdownDAO.Save error", slog.CustomTag("Error", err))
		return nil, err
	}

	nextCtx.Drawdown = request
	nextCtx.State = stPersistedDrawdown
	return nextCtx, nil
}
