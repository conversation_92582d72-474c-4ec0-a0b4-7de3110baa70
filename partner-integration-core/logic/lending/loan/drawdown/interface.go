package drawdown

import (
	"context"

	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/dto"
)

// Workflow defines the function required to be a drawdown workflow, implementation depends on the workflow.
//
//go:generate mockery --name Workflow --inpackage --case=underscore
type Workflow interface {
	// CreateWorkflow creates a new workflow execution.
	CreateWorkflow(context.Context, *dto.WorkflowRequest) (*dto.WorkflowResponse, error)
}
