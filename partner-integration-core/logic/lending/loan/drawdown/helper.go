package drawdown

import (
	"context"
	"errors"
	"time"

	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage"
)

func (w *WorkflowImpl) UpdateDBDrawdown(ctx context.Context, currDrawdown *storage.Drawdown, nextDrawdown *storage.Drawdown) error {
	nextDrawdown.UpdatedAt = time.Now()
	if err := w.DrawdownDAO.UpdateEntity(ctx, currDrawdown, nextDrawdown); err != nil {
		slog.FromContext(ctx).Warn(logDrawdown, "UpdateDBDrawdown error", slog.CustomTag("ReferenceID", nextDrawdown.ReferenceID), slog.CustomTag("Error", err))
		//	todo deep equal check if error is update no result by excluding updated_at field
		return err
	}
	slog.FromContext(ctx).Info(logDrawdown, "successfully updated nextState status for "+nextDrawdown.ReferenceID+" from "+currDrawdown.Status+" to "+nextDrawdown.Status)
	return nil
}

func castExecutionData(weExecData workflowengine.ExecutionData) (*ExecutionData, error) {
	currCtx, ok := weExecData.(*ExecutionData)
	if !ok {
		return nil, errors.New(logDrawdown + " " + "wrong context passed")
	}
	return currCtx, nil
}
