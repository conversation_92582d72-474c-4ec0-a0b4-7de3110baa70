package drawdown

import (
	"context"
	"encoding/json"
	"errors"

	"gitlab.com/gx-regional/dakota/servus/v2/data"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/common/partnerpayengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/lending/loanexp"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage"
)

func (w *WorkflowImpl) registerIntent(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}
	nextCtx := currCtx.Clone()
	acct, err := w.AccountDAO.FindOnSlave(ctx, data.EqualTo(storage.AccountColAccountID, nextCtx.Drawdown.LoanAccountID))
	if err != nil {
		slog.FromContext(ctx).Warn(logDrawdown, "AccountDAO.FindOnSlave error", slog.CustomTag("Error", err))
		return nil, err
	}
	res, err := w.LoanExpClient.RegisterPaymentIntentForAssetAccount(ctx, &loanexp.RegisterPaymentIntentForAssetAccountRequest{
		CommonHeaders: loanexp.CommonHeaders{
			UserID:    nextCtx.Drawdown.SafeID,
			ProfileID: nextCtx.ProfileID,
		},
		AccountID:      nextCtx.Drawdown.LoanAccountID,
		IdempotencyKey: nextCtx.Drawdown.ReferenceID,
		Amount: &loanexp.Money{
			CurrencyCode: nextCtx.Drawdown.Currency,
			Val:          nextCtx.Drawdown.PrincipalAmount,
		},
		Type:               string(partnerpayengine.LoanDisbursement),
		ProductVariantCode: acct[0].ProductVariantCode,
	})
	if err != nil {
		slog.FromContext(ctx).Warn(logDrawdown, "LoanExpClient.RegisterPaymentIntentForAssetAccount error", slog.CustomTag("Error", err))
		return nil, err
	}
	err = w.setPaymentIntentMetaData(nextCtx, res)
	if err != nil {
		return nil, err
	}
	nextCtx.Drawdown.LendingTransactionID = res.LoanTransactionID
	err = w.UpdateDBDrawdown(ctx, &currCtx.Drawdown, &nextCtx.Drawdown)
	if err != nil {
		return nil, err
	}
	nextCtx.State = stRegisteredIntent
	return nextCtx, nil
}

func (w *WorkflowImpl) setPaymentIntentMetaData(execData *ExecutionData, response *loanexp.RegisterPaymentIntentForAssetAccountResponse) error {
	if execData.Drawdown.Metadata == nil {
		execData.Drawdown.Metadata = json.RawMessage("{}")
	}
	var metadata map[string]interface{}
	err := json.Unmarshal(execData.Drawdown.Metadata, &metadata)
	if err != nil {
		return err
	}
	metadata["PaymentIntent"] = response.IntentID
	jsonMetadata, err := json.Marshal(metadata)
	if err != nil {
		return err
	}
	execData.Drawdown.Metadata = jsonMetadata
	return nil
}
