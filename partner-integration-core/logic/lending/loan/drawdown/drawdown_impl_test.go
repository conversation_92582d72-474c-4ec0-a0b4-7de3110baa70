package drawdown

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/common/wfcore"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/corebanking/accountservice"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/dto"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/server/config"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage/dbtype"
)

func TestDrawdownLogicImpl_CreateDrawdown(t *testing.T) {
	dummyErr := errors.New("dummy error")
	type fields struct {
		DrawdownWorkFlow     Workflow
		AccountServiceClient accountservice.IAccountService
	}
	type args struct {
		ctx     context.Context
		request *LogicDrawdownRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *LogicDrawdownResponse
		wantErr bool
	}{
		{
			name: "happy path",
			fields: fields{
				AccountServiceClient: func() accountservice.IAccountService {
					mct := accountservice.MockIAccountService{}
					mct.On("GetAccountDetails", mock.Anything, mock.Anything).Return(&accountservice.GetAccountDetailsResponse{
						Account: &accountservice.Account{
							CifNumber: "************",
						},
					}, nil).Once()
					return &mct
				}(),
				DrawdownWorkFlow: func() Workflow {
					mct := MockWorkflow{}
					mct.On("CreateWorkflow", mock.Anything, mock.Anything).Return(&dto.WorkflowResponse{
						Payload: &ExecutionData{
							Drawdown: storage.Drawdown{
								ReferenceID:          "ReferenceID",
								LendingTransactionID: "LendingTransactionID",
								Status:               storage.DrawdownStatusProcessing,
								PrincipalAmount:      15000,
								Currency:             "MYR",
							},
						},
					}, nil).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &LogicDrawdownRequest{
					Principal: DrawdownMoney{
						Amount:   15000,
						Currency: "MYR",
					},
					PreferredRepaymentDayOfMonth: "01",
					DestinationAccount: DrawdownAccountDetail{
						AccountNumber: "**********",
						SwiftCode:     "GXSPMYKL",
					},
				},
			},
			want: &LogicDrawdownResponse{
				ReferenceID: "ReferenceID",
				DrawdownID:  "LendingTransactionID",
				Status:      storage.DrawdownStatusProcessing,
				Amount:      "15000",
				Currency:    "MYR",
			},
			wantErr: false,
		},
		{
			name: "error path - AccountServiceClient no return CIF number",
			fields: fields{
				AccountServiceClient: func() accountservice.IAccountService {
					mct := accountservice.MockIAccountService{}
					mct.On("GetAccountDetails", mock.Anything, mock.Anything).Return(&accountservice.GetAccountDetailsResponse{}, nil).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &LogicDrawdownRequest{
					Principal: DrawdownMoney{
						Amount:   15000,
						Currency: "MYR",
					},
					PreferredRepaymentDayOfMonth: "01",
					DestinationAccount: DrawdownAccountDetail{
						AccountNumber: "**********",
						SwiftCode:     "GXSPMYKL",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "error path - AccountServiceClient failed on GetAccountDetails",
			fields: fields{
				AccountServiceClient: func() accountservice.IAccountService {
					mct := accountservice.MockIAccountService{}
					mct.On("GetAccountDetails", mock.Anything, mock.Anything).Return(nil, dummyErr).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &LogicDrawdownRequest{
					Principal: DrawdownMoney{
						Amount:   15000,
						Currency: "MYR",
					},
					PreferredRepaymentDayOfMonth: "01",
					DestinationAccount: DrawdownAccountDetail{
						AccountNumber: "**********",
						SwiftCode:     "GXSPMYKL",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "error path - invalid PreferredRepaymentDayOfMonth",
			fields: fields{
				AccountServiceClient: func() accountservice.IAccountService {
					mct := accountservice.MockIAccountService{}
					mct.On("GetAccountDetails", mock.Anything, mock.Anything).Return(&accountservice.GetAccountDetailsResponse{
						Account: &accountservice.Account{
							CifNumber: "************",
						},
					}, nil).Once()
					return &mct
				}(),
				DrawdownWorkFlow: func() Workflow {
					mct := MockWorkflow{}
					mct.On("CreateWorkflow", mock.Anything, mock.Anything).Return(&dto.WorkflowResponse{
						Payload: &ExecutionData{
							Drawdown: storage.Drawdown{
								ReferenceID:          "ReferenceID",
								LendingTransactionID: "LendingTransactionID",
								Status:               storage.DrawdownStatusProcessing,
								PrincipalAmount:      15000,
								Currency:             "MYR",
							},
						},
					}, nil).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &LogicDrawdownRequest{
					Principal: DrawdownMoney{
						Amount:   15000,
						Currency: "MYR",
					},
					PreferredRepaymentDayOfMonth: "xxx",
					DestinationAccount: DrawdownAccountDetail{
						AccountNumber: "**********",
						SwiftCode:     "GXSPMYKL",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "error path - DrawdownWorkFlow failed CreateWorkflow",
			fields: fields{
				AccountServiceClient: func() accountservice.IAccountService {
					mct := accountservice.MockIAccountService{}
					mct.On("GetAccountDetails", mock.Anything, mock.Anything).Return(&accountservice.GetAccountDetailsResponse{
						Account: &accountservice.Account{
							CifNumber: "************",
						},
					}, nil).Once()
					return &mct
				}(),
				DrawdownWorkFlow: func() Workflow {
					mct := MockWorkflow{}
					mct.On("CreateWorkflow", mock.Anything, mock.Anything).Return(nil, dummyErr).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &LogicDrawdownRequest{
					Principal: DrawdownMoney{
						Amount:   15000,
						Currency: "MYR",
					},
					PreferredRepaymentDayOfMonth: "01",
					DestinationAccount: DrawdownAccountDetail{
						AccountNumber: "**********",
						SwiftCode:     "GXSPMYKL",
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &drawdownLogicImpl{
				AccountServiceClient: tt.fields.AccountServiceClient,
				DrawdownWorkFlow:     tt.fields.DrawdownWorkFlow,
				BankInfo:             &config.BankInfo{SwiftCode: "GXSPMYKL"},
			}
			got, err := d.CreateDrawdown(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateDrawdown() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateDrawdown() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDrawdownLogicImpl_WorkflowResponseToLogicDrawdownResponse(t *testing.T) {
	tests := []struct {
		name     string
		input    *dto.WorkflowResponse
		expected *LogicDrawdownResponse
	}{
		{
			name: "successful conversion with all fields populated",
			input: &dto.WorkflowResponse{
				Payload: &ExecutionData{
					Drawdown: storage.Drawdown{
						ReferenceID:          "REF123456",
						LendingTransactionID: "TXN789012",
						Status:               "COMPLETED",
						StatusReason:         "Successfully processed",
						PrincipalAmount:      150000,
						Currency:             "MYR",
					},
				},
			},
			expected: &LogicDrawdownResponse{
				ReferenceID:  "REF123456",
				DrawdownID:   "TXN789012",
				Status:       "COMPLETED",
				StatusReason: "Successfully processed",
				Amount:       "150000",
				Currency:     "MYR",
			},
		},
		{
			name: "conversion with empty string fields",
			input: &dto.WorkflowResponse{
				Payload: &ExecutionData{
					Drawdown: storage.Drawdown{
						ReferenceID:          "",
						LendingTransactionID: "",
						Status:               "",
						StatusReason:         "",
						PrincipalAmount:      0,
						Currency:             "",
					},
				},
			},
			expected: &LogicDrawdownResponse{
				ReferenceID:  "",
				DrawdownID:   "",
				Status:       "",
				StatusReason: "",
				Amount:       "0",
				Currency:     "",
			},
		},
		{
			name: "conversion with negative amount",
			input: &dto.WorkflowResponse{
				Payload: &ExecutionData{
					Drawdown: storage.Drawdown{
						ReferenceID:          "REF-NEG-001",
						LendingTransactionID: "TXN-NEG-001",
						Status:               "FAILED",
						StatusReason:         "Insufficient funds",
						PrincipalAmount:      -50000,
						Currency:             "USD",
					},
				},
			},
			expected: &LogicDrawdownResponse{
				ReferenceID:  "REF-NEG-001",
				DrawdownID:   "TXN-NEG-001",
				Status:       "FAILED",
				StatusReason: "Insufficient funds",
				Amount:       "-50000",
				Currency:     "USD",
			},
		},
		{
			name: "conversion with large amount",
			input: &dto.WorkflowResponse{
				Payload: &ExecutionData{
					Drawdown: storage.Drawdown{
						ReferenceID:          "REF-LARGE-001",
						LendingTransactionID: "TXN-LARGE-001",
						Status:               "PENDING",
						StatusReason:         "Under review",
						PrincipalAmount:      999999999999,
						Currency:             "SGD",
					},
				},
			},
			expected: &LogicDrawdownResponse{
				ReferenceID:  "REF-LARGE-001",
				DrawdownID:   "TXN-LARGE-001",
				Status:       "PENDING",
				StatusReason: "Under review",
				Amount:       "999999999999",
				Currency:     "SGD",
			},
		},
		{
			name: "conversion with special characters in fields",
			input: &dto.WorkflowResponse{
				Payload: &ExecutionData{
					Drawdown: storage.Drawdown{
						ReferenceID:          "REF-@#$%^&*()",
						LendingTransactionID: "TXN-!@#$%",
						Status:               "IN_PROGRESS",
						StatusReason:         "Processing with special chars: @#$%^&*()",
						PrincipalAmount:      12345,
						Currency:             "EUR",
					},
				},
			},
			expected: &LogicDrawdownResponse{
				ReferenceID:  "REF-@#$%^&*()",
				DrawdownID:   "TXN-!@#$%",
				Status:       "IN_PROGRESS",
				StatusReason: "Processing with special chars: @#$%^&*()",
				Amount:       "12345",
				Currency:     "EUR",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := drawdownLogicImpl{}
			result := d.workflowResponseToLogicDrawdownResponse(tt.input)

			assert.Equal(t, tt.expected.ReferenceID, result.ReferenceID)
			assert.Equal(t, tt.expected.DrawdownID, result.DrawdownID)
			assert.Equal(t, tt.expected.Status, result.Status)
			assert.Equal(t, tt.expected.StatusReason, result.StatusReason)
			assert.Equal(t, tt.expected.Amount, result.Amount)
			assert.Equal(t, tt.expected.Currency, result.Currency)
		})
	}
}

func TestDrawdownLogicImpl_WorkflowResponseToLogicDrawdownResponse_PanicScenarios(t *testing.T) {
	tests := []struct {
		name  string
		input *dto.WorkflowResponse
	}{
		{
			name: "panic when payload is nil",
			input: &dto.WorkflowResponse{
				Payload: nil,
			},
		},
		{
			name: "panic when payload is not ExecutionData type",
			input: &dto.WorkflowResponse{
				Payload: "invalid_type",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := drawdownLogicImpl{}

			assert.Panics(t, func() {
				d.workflowResponseToLogicDrawdownResponse(tt.input)
			})
		})
	}
}

func TestDrawdownLogicImpl_WorkflowResponseToLogicDrawdownResponse_NilInput(t *testing.T) {
	t.Run("panic when input is nil", func(t *testing.T) {
		d := drawdownLogicImpl{}

		assert.Panics(t, func() {
			d.workflowResponseToLogicDrawdownResponse(nil)
		})
	})
}

// Benchmark test to measure performance
func BenchmarkDrawdownLogicImpl_WorkflowResponseToLogicDrawdownResponse(b *testing.B) {
	d := drawdownLogicImpl{}
	input := &dto.WorkflowResponse{
		Payload: &ExecutionData{
			Drawdown: storage.Drawdown{
				ReferenceID:          "REF123456",
				LendingTransactionID: "TXN789012",
				Status:               "COMPLETED",
				StatusReason:         "Successfully processed",
				PrincipalAmount:      150000,
				Currency:             "MYR",
			},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		d.workflowResponseToLogicDrawdownResponse(input)
	}
}

func TestWorkflowImpl_CreateWorkflow(t *testing.T) {
	dummyErr := errors.New("dummy error")
	type fields struct {
		WorkflowCore wfcore.IWorkflowCore
	}
	type args struct {
		ctx     context.Context
		request *dto.WorkflowRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *dto.WorkflowResponse
		wantErr bool
	}{
		{
			name: "happy path",
			fields: fields{
				WorkflowCore: func() wfcore.IWorkflowCore {
					mct := wfcore.MockIWorkflowCore{}
					mct.On("InitExecution", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mct.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(
						&ExecutionData{
							State:     stInit,
							ProfileID: "dummy-profileID",
							Drawdown: storage.Drawdown{
								ReferenceID:                  "ReferenceID",
								LendingTransactionID:         "LendingTransactionID",
								Status:                       storage.DrawdownStatusProcessing,
								PrincipalAmount:              15000,
								Currency:                     "MYR",
								PreferredRepaymentDayOfMonth: 1,
								DestinationAccount: &dbtype.DrawdownAccountDetail{
									AccountNumber: "**********",
									SwiftCode:     "GXSPMYKL",
								},
							},
						}, nil).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &dto.WorkflowRequest{
					Drawdown: &storage.Drawdown{
						ReferenceID:                  "ReferenceID",
						LendingTransactionID:         "LendingTransactionID",
						Status:                       storage.DrawdownStatusProcessing,
						PrincipalAmount:              15000,
						Currency:                     "MYR",
						PreferredRepaymentDayOfMonth: 1,
						DestinationAccount: &dbtype.DrawdownAccountDetail{
							AccountNumber: "**********",
							SwiftCode:     "GXSPMYKL",
						},
					},
				},
			},
			want: &dto.WorkflowResponse{
				InternalIdempotencyKey: "ReferenceID",
				Status:                 storage.DrawdownStatusProcessing,
				Payload: &ExecutionData{
					State:     stInit,
					ProfileID: "dummy-profileID",
					Drawdown: storage.Drawdown{
						ReferenceID:                  "ReferenceID",
						LendingTransactionID:         "LendingTransactionID",
						Status:                       storage.DrawdownStatusProcessing,
						PrincipalAmount:              15000,
						Currency:                     "MYR",
						PreferredRepaymentDayOfMonth: 1,
						DestinationAccount: &dbtype.DrawdownAccountDetail{
							AccountNumber: "**********",
							SwiftCode:     "GXSPMYKL",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "happy path - InitExecution with Execution Already Exists error",
			fields: fields{
				WorkflowCore: func() wfcore.IWorkflowCore {
					mct := wfcore.MockIWorkflowCore{}
					mct.On("InitExecution", mock.Anything, mock.Anything, mock.Anything).Return(workflowengine.ErrExecutionAlreadyExists).Once()
					mct.On("GetExecution", mock.Anything, mock.Anything, mock.Anything).Return(&ExecutionData{
						State:     stInit,
						ProfileID: "dummy-profileID",
						Drawdown: storage.Drawdown{
							ReferenceID:                  "ReferenceID",
							LendingTransactionID:         "LendingTransactionID",
							Status:                       storage.DrawdownStatusProcessing,
							PrincipalAmount:              15000,
							Currency:                     "MYR",
							PreferredRepaymentDayOfMonth: 1,
							DestinationAccount: &dbtype.DrawdownAccountDetail{
								AccountNumber: "**********",
								SwiftCode:     "GXSPMYKL",
							},
						},
					}, nil).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &dto.WorkflowRequest{
					Drawdown: &storage.Drawdown{
						ReferenceID:                  "ReferenceID",
						LendingTransactionID:         "LendingTransactionID",
						Status:                       storage.DrawdownStatusProcessing,
						PrincipalAmount:              15000,
						Currency:                     "MYR",
						PreferredRepaymentDayOfMonth: 1,
						DestinationAccount: &dbtype.DrawdownAccountDetail{
							AccountNumber: "**********",
							SwiftCode:     "GXSPMYKL",
						},
					},
				},
			},
			want: &dto.WorkflowResponse{
				InternalIdempotencyKey: "ReferenceID",
				Status:                 storage.DrawdownStatusProcessing,
				Payload: &ExecutionData{
					State:     stInit,
					ProfileID: "dummy-profileID",
					Drawdown: storage.Drawdown{
						ReferenceID:                  "ReferenceID",
						LendingTransactionID:         "LendingTransactionID",
						Status:                       storage.DrawdownStatusProcessing,
						PrincipalAmount:              15000,
						Currency:                     "MYR",
						PreferredRepaymentDayOfMonth: 1,
						DestinationAccount: &dbtype.DrawdownAccountDetail{
							AccountNumber: "**********",
							SwiftCode:     "GXSPMYKL",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "error path - InitExecution error",
			fields: fields{
				WorkflowCore: func() wfcore.IWorkflowCore {
					mct := wfcore.MockIWorkflowCore{}
					mct.On("InitExecution", mock.Anything, mock.Anything, mock.Anything).Return(dummyErr).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &dto.WorkflowRequest{
					Drawdown: &storage.Drawdown{
						ReferenceID:                  "ReferenceID",
						LendingTransactionID:         "LendingTransactionID",
						Status:                       storage.DrawdownStatusProcessing,
						PrincipalAmount:              15000,
						Currency:                     "MYR",
						PreferredRepaymentDayOfMonth: 1,
						DestinationAccount: &dbtype.DrawdownAccountDetail{
							AccountNumber: "**********",
							SwiftCode:     "GXSPMYKL",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "error path - GetExecution with error",
			fields: fields{
				WorkflowCore: func() wfcore.IWorkflowCore {
					mct := wfcore.MockIWorkflowCore{}
					mct.On("InitExecution", mock.Anything, mock.Anything, mock.Anything).Return(workflowengine.ErrExecutionAlreadyExists).Once()
					mct.On("GetExecution", mock.Anything, mock.Anything, mock.Anything).Return(nil, dummyErr).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &dto.WorkflowRequest{
					Drawdown: &storage.Drawdown{
						ReferenceID:                  "ReferenceID",
						LendingTransactionID:         "LendingTransactionID",
						Status:                       storage.DrawdownStatusProcessing,
						PrincipalAmount:              15000,
						Currency:                     "MYR",
						PreferredRepaymentDayOfMonth: 1,
						DestinationAccount: &dbtype.DrawdownAccountDetail{
							AccountNumber: "**********",
							SwiftCode:     "GXSPMYKL",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "error path - Execute error",
			fields: fields{
				WorkflowCore: func() wfcore.IWorkflowCore {
					mct := wfcore.MockIWorkflowCore{}
					mct.On("InitExecution", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mct.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(nil, dummyErr).Once()
					return &mct
				}(),
			},
			args: args{
				ctx: context.Background(),
				request: &dto.WorkflowRequest{
					Drawdown: &storage.Drawdown{
						ReferenceID:                  "ReferenceID",
						LendingTransactionID:         "LendingTransactionID",
						Status:                       storage.DrawdownStatusProcessing,
						PrincipalAmount:              15000,
						Currency:                     "MYR",
						PreferredRepaymentDayOfMonth: 1,
						DestinationAccount: &dbtype.DrawdownAccountDetail{
							AccountNumber: "**********",
							SwiftCode:     "GXSPMYKL",
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &WorkflowImpl{
				WorkflowCore: tt.fields.WorkflowCore,
			}
			got, err := d.CreateWorkflow(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateWorkflow() got = %v, want %v", got, tt.want)
			}
		})
	}
}
