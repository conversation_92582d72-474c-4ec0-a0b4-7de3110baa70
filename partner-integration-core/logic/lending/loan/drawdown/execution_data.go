package drawdown

import (
	"encoding/json"

	"gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage"
)

// ExecutionData is the state machine internal representation
type ExecutionData struct {
	ProfileID string
	State     workflowengine.State
	Drawdown  storage.Drawdown
}

// GetState gets current state of the machine.
func (e *ExecutionData) GetState() workflowengine.State {
	return e.State
}

// SetState sets the state of the machine.
func (e *ExecutionData) SetState(state workflowengine.State) {
	e.State = state
}

// Marshal marshals the state context.
func (e *ExecutionData) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

// Unmarshal unmarshals the state context.
func (e *ExecutionData) Unmarshal(byteData []byte) error {
	return json.Unmarshal(byteData, e)
}

// Clone clones a context.
func (e *ExecutionData) Clone() *ExecutionData {
	newCtx := *e
	return &newCtx
}
