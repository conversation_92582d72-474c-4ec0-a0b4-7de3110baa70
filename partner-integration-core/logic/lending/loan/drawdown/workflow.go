package drawdown

import (
	"context"

	we "gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/common/wfcore"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/lending/loanexp"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/payments/partnerpayengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/server/config"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage"
)

const (
	DrawdownWorkflow = "wf_drawdown"
	logDrawdown      = "fixed_term_loan_drawdown"
)

// states definition
const (
	stInit              = we.StateInit
	stPersistedDrawdown = we.State(100)
	stRegisteredIntent  = we.State(110)
	stUpdatedIntent     = we.State(120)
	stRequestedDrawdown = we.State(200)
	stExecutedIntent    = we.State(210)
	stDrawdownCompleted = we.State(900)
)

// events definition
const (
	evNoNeed                = we.EventNoNeed
	evPersistDrawdown       = we.Event(100)
	EvPersistDrawdownStream = we.Event(200)
)

// WorkflowImpl is the implementation of the workflow
type WorkflowImpl struct {
	WorkflowRetryConfig    *config.WorkflowRetryConfig              `inject:"config.workflowRetry"`
	DrawdownDAO            storage.IDrawdownDAO                     `inject:"storage.drawdownDAO"`
	AccountDAO             storage.IAccountDAO                      `inject:"storage.accountDAO"`
	PartnerpayEngineClient partnerpayengine.IPartnerpayEngineClient `inject:"client.partnerpayEngine"`
	LoanExpClient          loanexp.ILoanExpClient                   `inject:"client.loanExp"`
	WorkflowCore           wfcore.IWorkflowCore                     `inject:"workflow.core"`
}

func (w *WorkflowImpl) RegisterWorkflow() {
	drawdownWorkflow := we.NewWorkflow(DrawdownWorkflow, func() we.ExecutionData {
		return &ExecutionData{}
	})
	var transactionalRetryOption *we.TransitionOptions
	if retryOption := w.WorkflowRetryConfig.Drawdown.TransactionalRetryOption; retryOption != nil {
		// used for time-sensitive transactional states
		transactionalRetryOption = &we.TransitionOptions{
			RetryPolicy: &we.RetryPolicy{
				Interval:    retryOption.IntervalInSeconds,
				MaxAttempts: retryOption.MaxAttempt,
			},
		}
	}

	drawdownWorkflow.AddTransition(logDrawdown, stInit, evPersistDrawdown, w.persistDrawdown, nil, stPersistedDrawdown)
	drawdownWorkflow.AddTransition(logDrawdown, stPersistedDrawdown, evNoNeed, w.registerIntent, nil, stRegisteredIntent)
	drawdownWorkflow.AddTransition(logDrawdown, stRegisteredIntent, evNoNeed, dummyFunc, transactionalRetryOption, stUpdatedIntent)
	drawdownWorkflow.AddTransition(logDrawdown, stUpdatedIntent, evNoNeed, dummyFunc, transactionalRetryOption, stRequestedDrawdown)
	drawdownWorkflow.AddTransition(logDrawdown, stRequestedDrawdown, EvPersistDrawdownStream, dummyFunc, transactionalRetryOption, stExecutedIntent)
	drawdownWorkflow.AddTransition(logDrawdown, stExecutedIntent, evNoNeed, dummyFunc, transactionalRetryOption, stDrawdownCompleted)
	we.RegisterWorkflow(drawdownWorkflow)
}

func dummyFunc(ctx context.Context, transitionID string, data we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	return nil, nil
}
