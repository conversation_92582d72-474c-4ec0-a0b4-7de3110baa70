package drawdown

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/lending/loanexp"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage/dbtype"
)

func TestWorkflowImpl_registerIntent(t *testing.T) {
	dummyErr := errors.New("dummy-error")
	now := time.Now()
	execDataTemplate := &ExecutionData{
		State: stInit,
		Drawdown: storage.Drawdown{
			ID:                           1,
			PartnerID:                    "PartnerID",
			SafeID:                       "SafeID",
			ReferenceID:                  "ReferenceID",
			LendingTransactionID:         "LendingTransactionID",
			LoanAccountID:                "LoanAccountID",
			DestinationAccount:           &dbtype.DrawdownAccountDetail{},
			PrincipalAmount:              100,
			Currency:                     "MYR",
			TransactionDomain:            "TransactionDomain",
			TransactionType:              "TransactionType",
			TransactionSubtype:           "TransactionSubtype",
			Status:                       storage.DrawdownStatusProcessing,
			StatusReason:                 "",
			StatusReasonDescription:      "",
			LoanName:                     "LoanName",
			PreferredRepaymentDayOfMonth: 1,
			LoanTenorInMonths:            12,
			Metadata:                     nil,
			CreatedAt:                    now,
			UpdatedAt:                    now,
		},
	}
	type fields struct {
		AccountDAO    storage.IAccountDAO
		DrawdownDAO   storage.IDrawdownDAO
		LoanExpClient loanexp.ILoanExpClient
	}
	type args struct {
		ctx          context.Context
		transitionID string
		execData     workflowengine.ExecutionData
		params       interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ExecutionData
		wantErr bool
	}{
		{
			name: "happy path",
			fields: fields{
				LoanExpClient: func() loanexp.ILoanExpClient {
					mct := loanexp.MockILoanExpClient{}
					mct.On("RegisterPaymentIntentForAssetAccount", mock.Anything, mock.Anything).Return(&loanexp.RegisterPaymentIntentForAssetAccountResponse{
						LoanTransactionID: "LendingTransactionID",
						IntentID:          "IntentID",
					}, nil).Once()
					return &mct
				}(),
				AccountDAO: func() storage.IAccountDAO {
					mct := storage.MockIAccountDAO{}
					mct.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*storage.Account{
						{},
					}, nil).Once()
					return &mct
				}(),
				DrawdownDAO: func() storage.IDrawdownDAO {
					mct := storage.MockIDrawdownDAO{}
					mct.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					return &mct
				}(),
			},
			args: args{
				ctx:      context.Background(),
				execData: execDataTemplate,
			},
			want: &ExecutionData{
				State: stRegisteredIntent,
				Drawdown: storage.Drawdown{
					ID:                           1,
					PartnerID:                    "PartnerID",
					SafeID:                       "SafeID",
					ReferenceID:                  "ReferenceID",
					LendingTransactionID:         "LendingTransactionID",
					LoanAccountID:                "LoanAccountID",
					DestinationAccount:           &dbtype.DrawdownAccountDetail{},
					PrincipalAmount:              100,
					Currency:                     "MYR",
					TransactionDomain:            "TransactionDomain",
					TransactionType:              "TransactionType",
					TransactionSubtype:           "TransactionSubtype",
					Status:                       storage.DrawdownStatusProcessing,
					StatusReason:                 "",
					StatusReasonDescription:      "",
					LoanName:                     "LoanName",
					PreferredRepaymentDayOfMonth: 1,
					LoanTenorInMonths:            12,
					Metadata: func() json.RawMessage {
						metadata := map[string]interface{}{
							"PaymentIntent": "IntentID",
						}
						data, _ := json.Marshal(metadata)
						return data
					}(),
					CreatedAt: now,
				},
			},
			wantErr: false,
		},
		{
			name: "error path - Account not found",
			fields: fields{
				AccountDAO: func() storage.IAccountDAO {
					mct := storage.MockIAccountDAO{}
					mct.On("FindOnSlave", mock.Anything, mock.Anything).Return(nil, dummyErr).Once()
					return &mct
				}(),
			},
			args: args{
				ctx:      context.Background(),
				execData: execDataTemplate,
			},
			wantErr: true,
		},
		{
			name: "error path - register intent failed",
			fields: fields{
				AccountDAO: func() storage.IAccountDAO {
					mct := storage.MockIAccountDAO{}
					mct.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*storage.Account{
						{},
					}, nil).Once()
					return &mct
				}(),
				LoanExpClient: func() loanexp.ILoanExpClient {
					mct := loanexp.MockILoanExpClient{}
					mct.On("RegisterPaymentIntentForAssetAccount", mock.Anything, mock.Anything).Return(nil, dummyErr).Once()
					return &mct
				}(),
				DrawdownDAO: func() storage.IDrawdownDAO {
					mct := storage.MockIDrawdownDAO{}
					mct.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					return &mct
				}(),
			},
			args: args{
				ctx:      context.Background(),
				execData: execDataTemplate,
			},
			wantErr: true,
		},
		{
			name: "error path - update entity error",
			fields: fields{
				AccountDAO: func() storage.IAccountDAO {
					mct := storage.MockIAccountDAO{}
					mct.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*storage.Account{
						{},
					}, nil).Once()
					return &mct
				}(),
				LoanExpClient: func() loanexp.ILoanExpClient {
					mct := loanexp.MockILoanExpClient{}
					mct.On("RegisterPaymentIntentForAssetAccount", mock.Anything, mock.Anything).Return(&loanexp.RegisterPaymentIntentForAssetAccountResponse{
						LoanTransactionID: "LendingTransactionID",
						IntentID:          "IntentID",
					}, nil).Once()
					return &mct
				}(),
				DrawdownDAO: func() storage.IDrawdownDAO {
					mct := storage.MockIDrawdownDAO{}
					mct.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(dummyErr).Once()
					return &mct
				}(),
			},
			args: args{
				ctx:      context.Background(),
				execData: execDataTemplate,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WorkflowImpl{
				AccountDAO:    tt.fields.AccountDAO,
				DrawdownDAO:   tt.fields.DrawdownDAO,
				LoanExpClient: tt.fields.LoanExpClient,
			}
			got, err := w.registerIntent(tt.args.ctx, tt.args.transitionID, tt.args.execData, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("registerIntent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want == nil {
				return
			}
			currCtx, _ := castExecutionData(got)
			if !reflect.DeepEqual(currCtx.State, tt.want.State) {
				t.Errorf("registerIntent() got State = %v, want %v", currCtx.State, tt.want.State)
			}
			tt.want.Drawdown.UpdatedAt = currCtx.Drawdown.UpdatedAt
			if !reflect.DeepEqual(currCtx.Drawdown, tt.want.Drawdown) {
				t.Errorf("registerIntent() got Drawdown = %v, want %v", currCtx.Drawdown, tt.want.Drawdown)
			}
		})
	}
}
