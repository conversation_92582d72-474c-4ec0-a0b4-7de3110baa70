package drawdown

import (
	"context"
)

type LogicDrawdownRequest struct {
	ReferenceID                  string
	LoanAccountID                string
	LoanName                     string
	Principal                    Drawdown<PERSON><PERSON>
	DestinationAccount           DrawdownAccountDetail
	PreferredRepaymentDayOfMonth string
	LoanTenorInMonths            int
	Metadata                     map[string]interface{}
}

type DrawdownAccountDetail struct {
	AccountNumber string
	SwiftCode     string
}
type DrawdownMoney struct {
	Amount   int64
	Currency string
}

type LogicDrawdownResponse struct {
	ReferenceID  string
	DrawdownID   string
	Status       string
	StatusReason string
	Amount       string
	Currency     string
}

//go:generate mockery --name IDrawdownLogic --inpackage --case=underscore
type IDrawdownLogic interface {
	CreateDrawdown(ctx context.Context, req *LogicDrawdownRequest) (*LogicDrawdownResponse, error)
}
