package drawdown

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage/dbtype"
)

func TestWorkflowImpl_persistDrawdown(t *testing.T) {
	now := time.Now()
	execDataTemplate := &ExecutionData{
		State: stInit,
		Drawdown: storage.Drawdown{
			PartnerID:                    "PartnerID",
			SafeID:                       "SafeID",
			ReferenceID:                  "ReferenceID",
			LendingTransactionID:         "LendingTransactionID",
			LoanAccountID:                "LoanAccountID",
			DestinationAccount:           &dbtype.DrawdownAccountDetail{},
			PrincipalAmount:              100,
			Currency:                     "MYR",
			TransactionDomain:            "TransactionDomain",
			TransactionType:              "TransactionType",
			TransactionSubtype:           "TransactionSubtype",
			Status:                       storage.DrawdownStatusProcessing,
			StatusReason:                 "",
			StatusReasonDescription:      "",
			LoanName:                     "LoanName",
			PreferredRepaymentDayOfMonth: 1,
			LoanTenorInMonths:            12,
			Metadata:                     nil,
			CreatedAt:                    now,
			UpdatedAt:                    now,
		},
	}
	type fields struct {
		DrawdownDAO storage.IDrawdownDAO
	}
	type args struct {
		ctx          context.Context
		transitionID string
		execData     workflowengine.ExecutionData
		params       interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ExecutionData
		wantErr bool
	}{
		{
			name: "happy path",
			fields: fields{
				DrawdownDAO: func() storage.IDrawdownDAO {
					mct := storage.MockIDrawdownDAO{}
					mct.On("Save", mock.Anything,
						mock.AnythingOfType("*storage.Drawdown")).Run(func(args mock.Arguments) {
						resp := args.Get(1).(*storage.Drawdown)
						resp.ID = 1
						resp.UpdatedAt = time.Now()
					}).Return(nil).Once()
					return &mct
				}(),
			},
			args: args{
				ctx:      context.Background(),
				execData: execDataTemplate,
			},
			want: &ExecutionData{
				State: stPersistedDrawdown,
				Drawdown: storage.Drawdown{
					ID:                           1,
					PartnerID:                    "PartnerID",
					SafeID:                       "SafeID",
					ReferenceID:                  "ReferenceID",
					LendingTransactionID:         "LendingTransactionID",
					LoanAccountID:                "LoanAccountID",
					DestinationAccount:           &dbtype.DrawdownAccountDetail{},
					PrincipalAmount:              100,
					Currency:                     "MYR",
					TransactionDomain:            "TransactionDomain",
					TransactionType:              "TransactionType",
					TransactionSubtype:           "TransactionSubtype",
					Status:                       storage.DrawdownStatusProcessing,
					StatusReason:                 "",
					StatusReasonDescription:      "",
					LoanName:                     "LoanName",
					PreferredRepaymentDayOfMonth: 1,
					LoanTenorInMonths:            12,
					Metadata:                     nil,
					CreatedAt:                    now,
				},
			},
			wantErr: false,
		},
		{
			name: "error path",
			fields: fields{
				DrawdownDAO: func() storage.IDrawdownDAO {
					mct := storage.MockIDrawdownDAO{}
					mct.On("Save", mock.Anything, mock.Anything).Return(errors.New("dummy-error")).Once()
					return &mct
				}(),
			},
			args: args{
				ctx:      context.Background(),
				execData: execDataTemplate,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WorkflowImpl{
				DrawdownDAO: tt.fields.DrawdownDAO,
			}
			got, err := w.persistDrawdown(tt.args.ctx, tt.args.transitionID, tt.args.execData, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("persistDrawdown() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want == nil {
				return
			}
			currCtx, _ := castExecutionData(got)
			if !reflect.DeepEqual(currCtx.State, tt.want.State) {
				t.Errorf("persistDrawdown() got State = %v, want %v", currCtx.State, tt.want.State)
			}
			tt.want.Drawdown.UpdatedAt = currCtx.Drawdown.UpdatedAt
			if !reflect.DeepEqual(currCtx.Drawdown, tt.want.Drawdown) {
				t.Errorf("persistDrawdown() got Drawdown = %v, want %v", currCtx.Drawdown, tt.want.Drawdown)
			}
		})
	}
}
