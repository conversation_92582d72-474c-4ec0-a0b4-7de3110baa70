package drawdown

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	commonCtx "gitlab.com/gx-regional/dakota/common/context"
	"gitlab.com/gx-regional/dakota/payment/partnerpay-engine/api"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dakota/workflowengine"
	activeprofile "gitlab.com/gx-regional/dbmy/common/active-profile/v2"
	"gitlab.com/gx-regional/dbmy/partner-integration/common/constant"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/corebanking/accountservice"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/dto"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/server/config"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage/dbtype"
)

// NewDrawdownLogic ...
func NewDrawdownLogic() IDrawdownLogic {
	return &drawdownLogicImpl{}
}

type drawdownLogicImpl struct {
	DrawdownWorkFlow     Workflow                       `inject:"workflow.drawdown"`
	AccountServiceClient accountservice.IAccountService `inject:"client.accountService"`
	BankInfo             *config.BankInfo               `inject:"config.bankInfo"`
}

func (d drawdownLogicImpl) CreateDrawdown(ctx context.Context, req *LogicDrawdownRequest) (*LogicDrawdownResponse, error) {
	accountRes, err := d.AccountServiceClient.GetAccountDetails(ctx, &accountservice.GetAccountDetailsRequest{
		AccountID: req.LoanAccountID,
	})
	if err != nil {
		slog.FromContext(ctx).Warn(logDrawdown, "AccountServiceClient.GetAccountDetails error", slog.CustomTag("Error", err))
		return nil, err
	}
	if accountRes.Account == nil || accountRes.Account.CifNumber == "" {
		slog.FromContext(ctx).Warn(logDrawdown, "AccountServiceClient.GetAccountDetails response Account is nil")
		return nil, errors.New("empty account")
	}
	preferredRepaymentDayOfMonth, err := strconv.Atoi(req.PreferredRepaymentDayOfMonth)
	if err != nil {
		slog.FromContext(ctx).Error(logDrawdown, "invalid PreferredRepaymentDayOfMonth is nil", slog.Error(err))
		return nil, err
	}
	dataByte, err := json.Marshal(req.Metadata)
	if err != nil {
		slog.FromContext(ctx).Warn(logDrawdown, "invalid metadata", slog.Error(err))
		return nil, err
	}
	transactionDomain := storage.TransactionDomainLending
	if activeprofile.GetProfileType(accountRes.Account.CifNumber) == activeprofile.BUSINESS {
		transactionDomain = storage.TransactionDomainBizLending
	}

	transactionSubtype := constant.TransactionTypePayment
	if req.DestinationAccount.SwiftCode == d.BankInfo.SwiftCode {
		transactionSubtype = constant.TransactionTypeIntrabank
	}
	wfRequest := &dto.WorkflowRequest{
		ProfileID: accountRes.Account.CifNumber,
		Drawdown: &storage.Drawdown{
			PartnerID:     commonCtx.GetOAuthPartnerID(ctx),
			SafeID:        commonCtx.GetUserID(ctx),
			ReferenceID:   req.ReferenceID,
			LoanAccountID: req.LoanAccountID,
			DestinationAccount: &dbtype.DrawdownAccountDetail{
				AccountNumber: req.DestinationAccount.AccountNumber,
				SwiftCode:     req.DestinationAccount.SwiftCode,
			},
			Status:                       storage.DrawdownStatusProcessing,
			PrincipalAmount:              req.Principal.Amount,
			Currency:                     req.Principal.Currency,
			TransactionType:              storage.TransactionTypeDrawdown,
			TransactionDomain:            transactionDomain,
			TransactionSubtype:           transactionSubtype,
			LoanName:                     req.LoanName,
			PreferredRepaymentDayOfMonth: preferredRepaymentDayOfMonth,
			LoanTenorInMonths:            req.LoanTenorInMonths,
			Metadata:                     dataByte,
		},
	}
	response, err := d.DrawdownWorkFlow.CreateWorkflow(ctx, wfRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logDrawdown, "CreateWorkflow error", slog.CustomTag("Error", err))
		return nil, err
	}
	return d.workflowResponseToLogicDrawdownResponse(response), nil
}

func (d drawdownLogicImpl) workflowResponseToLogicDrawdownResponse(resp *dto.WorkflowResponse) *LogicDrawdownResponse {
	execData := resp.Payload.(*ExecutionData)
	return &LogicDrawdownResponse{
		ReferenceID:  execData.Drawdown.ReferenceID,
		DrawdownID:   execData.Drawdown.LendingTransactionID,
		Status:       execData.Drawdown.Status,
		StatusReason: execData.Drawdown.StatusReason,
		Amount:       fmt.Sprintf("%d", execData.Drawdown.PrincipalAmount),
		Currency:     execData.Drawdown.Currency,
	}
}

func (w WorkflowImpl) CreateWorkflow(ctx context.Context, workflowRequest *dto.WorkflowRequest) (*dto.WorkflowResponse, error) {
	var err error
	err = w.WorkflowCore.InitExecution(ctx, workflowengine.Execution{
		WorkflowID: DrawdownWorkflow,
		RequestID:  workflowRequest.Drawdown.ReferenceID,
	}, &ExecutionData{
		ProfileID: workflowRequest.ProfileID,
		Drawdown:  *workflowRequest.Drawdown,
	})
	if err != nil {
		if !errors.Is(err, workflowengine.ErrExecutionAlreadyExists) {
			slog.FromContext(ctx).Warn(logDrawdown, "InitExecution err...", slog.Error(err))
			return nil, api.DefaultInternalServerError
		}
		var execData workflowengine.ExecutionData
		execData, err = w.WorkflowCore.GetExecution(ctx, workflowengine.Execution{
			WorkflowID: DrawdownWorkflow,
			RequestID:  workflowRequest.Drawdown.ReferenceID,
		})
		if err != nil {
			slog.FromContext(ctx).Warn(logDrawdown, "GetExecution err...", slog.Error(err))
			return nil, api.DefaultInternalServerError
		}
		return &dto.WorkflowResponse{
			Status:                 execData.(*ExecutionData).Drawdown.Status,
			InternalIdempotencyKey: execData.(*ExecutionData).Drawdown.ReferenceID,
			Payload:                execData.(*ExecutionData),
		}, nil
	}

	execData, err := w.WorkflowCore.Execute(ctx, workflowengine.Execution{
		WorkflowID:     DrawdownWorkflow,
		RequestID:      workflowRequest.Drawdown.ReferenceID,
		ExecutionEvent: evPersistDrawdown,
	}, nil)
	if err != nil {
		slog.FromContext(ctx).Warn(logDrawdown, "Execute err...", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}
	return &dto.WorkflowResponse{
		Status:                 storage.DrawdownStatusProcessing,
		InternalIdempotencyKey: execData.(*ExecutionData).Drawdown.ReferenceID,
		Payload:                execData.(*ExecutionData),
	}, nil
}
