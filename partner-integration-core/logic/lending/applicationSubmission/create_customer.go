package applicationSubmission

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/common/stringutil"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/onboarding/customermaster"
)

func (w *WorkflowImpl) updateCustomerMaster(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}
	nextCtx := currCtx.Clone()
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("customerID", nextCtx.CustomerID), slog.CustomTag("applicationID", nextCtx.OnboardingApplicationID))
	slog.FromContext(ctx).Info(LoanAppSubmissionWorkflowLogTag, "updateCustomerMaster()")

	/**
	TODO
	pull application from application-service
	update customer info with what we've received in application (customer ady created via createshellcustomer endpoint
	make sure to set customer's status to inactive
	*/

	if nextCtx.CreateLoanApplicationRequest.Customer == nil {
		return nextCtx, errors.New("unexpected nextCtx.CreateLoanApplicationRequest.Customer to be nil")
	}
	if nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails == nil {
		return nextCtx, errors.New("unexpected nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails to be nil")
	}
	if nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.IdentityDocument == nil {
		return nextCtx, errors.New("unexpected nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.IdentityDocument to be nil")
	}
	if nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.IdentityDocument.Type != IdentityDocumentType_MYKAD {
		return nextCtx, errors.New("unexpected nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.IdentityDocument.Type to not be MYKAD")
	}

	cleanedNRIC := stringutil.RemoveHyphensAndWhitespace(nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.IdentityDocument.Value)
	dob, err := stringutil.ExtractDOBFromIDNumber(cleanedNRIC)
	if err != nil {
		return nextCtx, fmt.Errorf("error at ExtractDOBFromIDNumber(). err=%s", err.Error())
	}
	formattedDob := dob.Format(time.DateOnly)

	derivedGender, err := stringutil.DeriveGenderFromNRIC(cleanedNRIC)
	if err != nil {
		return nextCtx, fmt.Errorf("error at DeriveGenderFromNRIC(). err=%s", err.Error())
	}

	gender, err := convertGender(derivedGender)
	if err != nil {
		return nextCtx, fmt.Errorf("error at convertGender(). err=%s", err.Error())
	}

	email := nextCtx.CreateLoanApplicationRequest.Customer.ContactInformation.Email
	phoneNumber := nextCtx.CreateLoanApplicationRequest.Customer.ContactInformation.ContactNumber

	employments, err := convertEmployment(nextCtx.CreateLoanApplicationRequest.Customer.EmploymentDetails)
	if err != nil {
		return nextCtx, fmt.Errorf("error at convertEmployment(). err=%s", err.Error())
	}

	nonBankDebtAmount, err := convertNonBankDebtAmount(nextCtx.CreateLoanApplicationRequest.Product.NonFinancialInstitutionLoan.Amount)
	if err != nil {
		return nextCtx, fmt.Errorf("error at convertNonBankDebtAmount. err=%s", err.Error())
	}

	// convenience, to get pointer of values
	var (
		customerStatusInactive            = customermaster.CustomerStatus_Inactive
		customerTypeTNG                   = customermaster.CustomerType_TNG
		idTypeMyKad                       = customermaster.IdentityType_MYKAD
		malaysiaISO31661Alpha3CountryCode = "MYS"
		contactTypePrimary                = customermaster.ContactType_Primary
		addressTypeRegistered             = customermaster.AddressType_Registered
		addressTypeMailing                = customermaster.AddressType_Mailing
		tncAgreementIDAllowDataSharing    = "allowDataSharing"
		tncAgreementIDAllowCreditProducts = "allowCreditProductConsent"
		tncAgreementIDAllowMarketing      = "allowMarketingConsent"
		tncAgreementTypeOnboarding        = customermaster.AgreementType_Onboarding
		purposeOfAccountFinancialPlanning = 5
	)

	_, err = w.CustomerMasterClient.CreateCustomer(ctx, &customermaster.CreateCustomerRequest{
		ID: nextCtx.CustomerID,
		Customer: &customermaster.Customer{
			Name:           &nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.Name,
			Gender:         &gender,
			DateOfBirth:    &formattedDob,
			Nationality:    &nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.Nationality,
			CountryOfBirth: nil, // todo missing country of birth
			Status:         &customerStatusInactive,
			Type:           &customerTypeTNG,
			Identities: []*customermaster.Identity{
				{
					IDType:   &idTypeMyKad,
					IDNumber: &nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.IdentityDocument.Value,
					Country:  &malaysiaISO31661Alpha3CountryCode,
				},
			},
			Contacts: []*customermaster.ContactDetail{
				{
					ContactType:   &contactTypePrimary,
					Email:         &email.Value,
					EmailVerified: &email.IsVerified,
					PhoneNumber:   &phoneNumber.Value,
				},
			},
			Addresses: []*customermaster.Address{
				{
					AddressType: &addressTypeRegistered,
					City:        &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Registered.Town,
					State:       &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Registered.State,
					Country:     &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Registered.Country,
					PostalCode:  &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Registered.Postcode,
					AddrLine1:   &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Registered.Line1,
					AddrLine2:   &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Registered.Line2,
				},
				{
					AddressType: &addressTypeMailing,
					City:        &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Mailing.Town,
					State:       &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Mailing.State,
					Country:     &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Mailing.Country,
					PostalCode:  &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Mailing.Postcode,
					AddrLine1:   &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Mailing.Line1,
					AddrLine2:   &nextCtx.CreateLoanApplicationRequest.Customer.Addresses.Mailing.Line2,
				},
			},
			TnCAgreements: []*customermaster.TnCAgreement{
				{
					AgreementID:   &tncAgreementIDAllowDataSharing,
					AgreementType: &tncAgreementTypeOnboarding,
					Status:        convertConsent(nextCtx.CreateLoanApplicationRequest.Customer.Consents.AllowDataSharing),
				},
				{
					AgreementID:   &tncAgreementIDAllowCreditProducts,
					AgreementType: &tncAgreementTypeOnboarding,
					Status:        convertConsent(nextCtx.CreateLoanApplicationRequest.Customer.Consents.AllowCreditProducts),
				},
				{
					AgreementID:   &tncAgreementIDAllowMarketing,
					AgreementType: &tncAgreementTypeOnboarding,
					Status:        convertConsent(nextCtx.CreateLoanApplicationRequest.Customer.Consents.AllowMarketing),
				},
			},
			Employments: employments,
			ECDDInfo: &customermaster.ECDDInfo{
				PurposeOfAccount: &purposeOfAccountFinancialPlanning,
				PurposeOfCreatingAccount: &[]int{
					purposeOfAccountFinancialPlanning,
				},
				SourceOfWealth:               nil, // where to get?
				SourceOfFunds:                nil, // where to get?
				AnticipatedAmount:            nil, // where to get?
				AnticipatedNumOfTransactions: nil, // where to get?
			},
			IsResident:        convertIsResident(nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.ResidencyStatus),
			Religion:          nil, // where to get ?
			NonBankDebtAmount: &nonBankDebtAmount,
			PurposesOfCreatingAccount: &customermaster.PurposesOfCreatingAccount{
				customermaster.PurposeOfCreatingAccount_FinancialPlanning,
			},
		},
	})
	if err != nil {
		return nextCtx, fmt.Errorf("error at CustomerMasterClient.CreateCustomer(). err=%s", err.Error())
	}

	nextCtx.SetState(stCreateEcosystemId)
	return nextCtx, nil
}

func (w *WorkflowImpl) createEcosystemID(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}

	/**
	TODO
	call customer-experience to create ecosystem id
	not sure if there's even an endpoint for this, need check
	*/

	nextCtx := currCtx.Clone()
	nextCtx.SetState(stUploadCustomerDocument)
	return nextCtx, nil
}
