package applicationSubmission

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitlab.com/gx-regional/dakota/common/servicename"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	we "gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/hermes"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/utils/s3"

	"github.com/go-resty/resty/v2"
	"github.com/samber/lo"
)

var restClient = resty.New()

const (
	documentUploadDestinationType = "partnerLendingIncomeEstimation"
	documentStatusUploaded        = "UPLOADED"
)

func (w *WorkflowImpl) uploadCustomerDocumentToHermes(ctx context.Context, transitionID string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}
	// todo: upload customer identity documents
	nextCtx := currCtx.Clone()

	nextCtx.SetState(stUploadCustomerDocumentCompleted)
	return nextCtx, nil
}

func (w *WorkflowImpl) uploadLoanApplicationDocument(ctx context.Context, transitionID string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}
	nextCtx := currCtx.Clone()

	// get the document details from execution data
	sources := currCtx.CreateLoanApplicationRequest.Product.IncomeAnalysis.Sources
	documents := lo.FlatMap(sources, func(source IncomeSourceAnalysis, _ int) []SupportingDocumentMetadata {
		return source.Epf.SupportingDocuments
	})

	if len(documents) == 0 {
		return nil, errors.New("no documents to upload")
	}
	var currentDocument SupportingDocumentMetadata
	for _, doc := range documents {
		if !lo.HasKey(nextCtx.CustomerDocuments, doc.FileName) {
			currentDocument = doc
			break
		}
	}
	// we couldn't find any more documents to upload, this means all docs have been uploaded
	if currentDocument.FileName == "" {
		nextCtx.SetState(stUploadLoanApplicationDocumentCompleted)
	}

	// download from ingress s3
	fileBytes, err := s3.DownloadFileFromS3(ctx, w.S3Client, w.Config.DocumentSourceBucket, currentDocument.FilePath)
	if err != nil {
		err = fmt.Errorf("error at DownloadFileFromS3(). err=%s", err.Error())
		slog.FromContext(ctx).Error(LoanAppSubmissionWorkflowLogTag, err.Error())
		return nil, err
	}

	// create presigned url
	presignedDocument := hermes.DocUploadReqDetails{
		FileName:      currentDocument.FileName,
		FileExtension: strings.ToUpper(currentDocument.FileType),
		FileType:      documentUploadDestinationType,
		Metadata: map[string]string{
			"checksum":     currentDocument.Checksum,
			"documentType": string(currentDocument.DocumentType),
		},
		FolderName: loanDocumentDestinationFolderPath(nextCtx, currentDocument),
	}

	presignedRes, err := w.HermesClient.GetUploadURLV2(ctx, &hermes.GetUploadURLRequest{
		CommonHeaders: hermes.CommonHeaders{
			UserID: nextCtx.CustomerID,
		},
		Documents: []hermes.DocUploadReqDetails{presignedDocument},
		CreatedBy: servicename.PartnerIntegrationAPI.ToString(),
	})

	if err != nil {
		err = fmt.Errorf("error at HermesClient.GetUploadURLV2(). err=%s", err.Error())
		slog.FromContext(ctx).Error(LoanAppSubmissionWorkflowLogTag, err.Error())
		return nil, err
	}
	if len(presignedRes.Documents) == 0 {
		err = fmt.Errorf("no presigned url returned from hermes")
		slog.FromContext(ctx).Error(LoanAppSubmissionWorkflowLogTag, err.Error())
		return nil, err
	}

	presignedDetail := presignedRes.Documents[0]

	// upload to s3
	err = w.presignedUpload(ctx, fileBytes, presignedDetail)
	if err != nil {
		err = fmt.Errorf("error at presignedUpload(). err=%s", err.Error())
		slog.FromContext(ctx).Error(LoanAppSubmissionWorkflowLogTag, err.Error())
		return nil, err
	}
	if nextCtx.LoanApplicationDocuments == nil {
		nextCtx.LoanApplicationDocuments = make(map[string]DocumentUploadStatus)
	}
	nextCtx.LoanApplicationDocuments[currentDocument.FileName] = DocumentUploadStatus{
		FileName:   currentDocument.FileName,
		FilePath:   currentDocument.FilePath,
		DocumentID: presignedDetail.DocumentID,
		Status:     documentStatusUploaded,
	}

	nextCtx.SetState(stUploadLoanApplicationDocument)
	return nextCtx, nil
}

func loanDocumentDestinationFolderPath(data *ExecutionData, document SupportingDocumentMetadata) string {
	// sample from perfios
	// s3://dev-backend-perfios-adapter-dbmy-s3/lendingIncomeEstimation/0fbf7e5a-0941-4f09-a36c-bff2621e244a/applications/1f55f72a-3601-4bc8-a654-29f344f6f5a1/uploadDocuments/epf_statements/a8883102-8e53-416f-8c6b-44621cbc2685/
	const partnerCode = "tng"
	return fmt.Sprintf("partner/%s/lendingIncomeEstimation/%s/%s", partnerCode, data.CreateLoanApplicationRequest.ReferenceID, document.DocumentType)
}

func (w *WorkflowImpl) presignedUpload(ctx context.Context, fileBytes []byte, presignedDetail hermes.DocUploadResDetails) error {
	headers := map[string]string{}
	for k, v := range presignedDetail.PresignedHeaders {
		headers[k] = v
	}
	uploadFunc := restClient.R().SetHeaders(headers).SetBody(fileBytes).Put
	uploadFunc = lo.Ternary(lo.IsNotNil(w.restClientFunc), w.restClientFunc, uploadFunc)

	res, err := uploadFunc(presignedDetail.PresignedURL)
	if err != nil {
		return err
	}
	if res.StatusCode() != 200 {
		return fmt.Errorf("failed to upload file. status code: %d, response: %s", res.StatusCode(), res.String())
	}
	return nil
}
