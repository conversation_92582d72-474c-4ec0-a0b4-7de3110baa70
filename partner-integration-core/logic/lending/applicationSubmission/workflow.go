package applicationSubmission

import (
	"gitlab.com/gx-regional/dakota/common/aws/s3client"
	we "gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/hermes"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/onboarding/customermaster"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/server/config"

	"github.com/go-resty/resty/v2"
)

const (
	LoanAppSubmissionWorkflowID     = "wf_loan_app_submission"
	LoanAppSubmissionWorkflowLogTag = "loan_app_submission"
)

const (
	stInit                                   = we.StateInit
	stCreateEcosystemId                      = we.State(100)
	stUploadCustomerDocument                 = we.State(200)
	stUploadCustomerDocumentCompleted        = we.State(205)
	stUploadLoanApplicationDocument          = we.State(210)
	stUploadLoanApplicationDocumentCompleted = we.State(215)
	stCreateLoanApplication                  = we.State(300)
)

const (
	EvStart = we.Event(1)
)

// Config ...
type Config struct {
	DocumentSourceBucket      string
	DocumentDestinationBucket string
}

// WorkflowImpl ...
type WorkflowImpl struct {
	WorkflowRetryConfig  *config.WorkflowRetryConfig          `inject:"config.workflowRetry"`
	CustomerMasterClient customermaster.ICustomerMasterClient `inject:"client.customerMaster"`
	HermesClient         hermes.IHermesClient                 `inject:"client.hermes"`
	S3Client             s3client.S3                          `inject:"client.S3"`
	Config               Config
	restClientFunc       func(string) (*resty.Response, error) // for testing injection
}

// RegisterWorkflow should be called during initialisation to register individual workflow to the workflow engine
func (w *WorkflowImpl) RegisterWorkflow() {
	wf := we.NewWorkflow(LoanAppSubmissionWorkflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})
	var transactionalRetryOption *we.TransitionOptions
	if retryOption := w.WorkflowRetryConfig.LoanApplicationSubmission.TransactionalRetryOption; retryOption != nil {
		transactionalRetryOption = &we.TransitionOptions{
			RetryPolicy: &we.RetryPolicy{
				Interval:    retryOption.IntervalInSeconds,
				MaxAttempts: retryOption.MaxAttempt,
			},
		}
	}

	wf.AddTransition(LoanAppSubmissionWorkflowLogTag, stInit, EvStart, w.updateCustomerMaster, transactionalRetryOption, stCreateEcosystemId)
	wf.AddTransition(LoanAppSubmissionWorkflowLogTag, stCreateEcosystemId, we.EventNoNeed, w.createEcosystemID, transactionalRetryOption, stUploadCustomerDocument)
	wf.AddTransition(LoanAppSubmissionWorkflowLogTag, stUploadCustomerDocument, we.EventNoNeed, w.uploadCustomerDocumentToHermes, transactionalRetryOption, stUploadCustomerDocument, stUploadCustomerDocumentCompleted)
	wf.AddTransition(LoanAppSubmissionWorkflowLogTag, stUploadCustomerDocumentCompleted, we.EventNoNeed, w.uploadCustomerDocumentToHermes, transactionalRetryOption, stUploadLoanApplicationDocument)
	wf.AddTransition(LoanAppSubmissionWorkflowLogTag, stUploadLoanApplicationDocument, we.EventNoNeed, w.uploadLoanApplicationDocument, transactionalRetryOption, stUploadLoanApplicationDocument, stUploadLoanApplicationDocumentCompleted)
	wf.AddTransition(LoanAppSubmissionWorkflowLogTag, stUploadLoanApplicationDocumentCompleted, we.EventNoNeed, w.uploadLoanApplicationDocument, transactionalRetryOption, stCreateLoanApplication)

	we.RegisterWorkflow(wf)
}
