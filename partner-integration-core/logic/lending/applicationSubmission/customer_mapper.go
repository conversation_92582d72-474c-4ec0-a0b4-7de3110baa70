package applicationSubmission

import (
	"errors"
	"fmt"

	"gitlab.com/gx-regional/dbmy/partner-integration/common/stringutil"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/onboarding/customermaster"
)

func convertNonBankDebtAmount(r int64) (uint, error) {
	if r < 0 {
		return 0, fmt.Errorf("non bank debt amount must be greater than zero. input=%d", r)
	}
	return uint(r), nil
}

func convertIsResident(r ResidencyStatus) *bool {
	var (
		t = true
		f = false
	)
	if r == ResidencyStatus_R {
		return &t
	}
	return &f
}

func convertEmployment(e *EmploymentDetails) ([]*customermaster.Employment, error) {
	if e == nil {
		return nil, errors.New("employmentDetails unexpectedly is nil")
	}
	employmentType, err := convertEmploymentType(e.Type)
	if err != nil {
		return nil, err
	}

	occupation, err := convertOccupation(e.Type, e.Occupation)
	if err != nil {
		return nil, err
	}
	natureOfBusiness, err := convertNatureOfBusiness(e.Type, e.NatureOfBusiness)
	if err != nil {
		return nil, err
	}
	monthlyIncome, err := convertMonthlyIncome(e.MonthlyIncome)
	if err != nil {
		return nil, err
	}

	return []*customermaster.Employment{
		{
			Type:             &employmentType,
			Occupation:       &occupation,
			EmployerName:     &e.EmployerName,
			NatureOfBusiness: &natureOfBusiness,
			MonthlyIncome:    &monthlyIncome,
		},
	}, nil
}

// convertNatureOfBusiness maps values like BELOW from api to 01 from application-service
// this mapping is maintained manually, and could be prone to being outdated
func convertMonthlyIncome(i EmploymentMonthlyIncome) (string, error) {
	monthlyIncomeMap := map[EmploymentMonthlyIncome]string{
		EmploymentMonthlyIncome_BELOW_2000:  "01",
		EmploymentMonthlyIncome_2000_2999:   "02",
		EmploymentMonthlyIncome_3000_4999:   "03",
		EmploymentMonthlyIncome_5000_6999:   "04",
		EmploymentMonthlyIncome_7000_8999:   "05",
		EmploymentMonthlyIncome_9000_10999:  "06",
		EmploymentMonthlyIncome_11000_14999: "07",
		EmploymentMonthlyIncome_15000_24999: "08",
		EmploymentMonthlyIncome_ABOVE_25000: "09",
	}

	if val, ok := monthlyIncomeMap[i]; ok {
		return val, nil
	}
	return "", fmt.Errorf("unsupported employment monthly income %s", i)
}

// convertNatureOfBusiness maps values like PV_NOB_AGRIC_FOREST from api to 01 from application-service
// this mapping is maintained manually, and could be prone to being outdated
func convertNatureOfBusiness(t EmploymentDetailsType, n NatureOfBusiness) (string, error) {
	switch t {
	//nolint:dupl
	case EmploymentDetailsType_PUBLIC_SECTOR_EMPLOYEE:
		publicEmployment := map[NatureOfBusiness]string{
			NatureOfBusiness_PB_NOB_AGRIC_FOREST:     "01",
			NatureOfBusiness_PB_NOB_BUSINESS:         "04",
			NatureOfBusiness_PB_NOB_CHEMICAL:         "24",
			NatureOfBusiness_PB_NOB_CONST_REAL_EST:   "05",
			NatureOfBusiness_PB_NOB_GOV_OTHERS:       "28",
			NatureOfBusiness_PB_NOB_GOV_DEFENSE:      "29",
			NatureOfBusiness_PB_NOB_EDUCATION:        "06",
			NatureOfBusiness_PB_NOB_ENT_OTHERS:       "30",
			NatureOfBusiness_PB_NOB_ENT_CASINO_KTV:   "31",
			NatureOfBusiness_PB_NOB_FINANCE:          "07",
			NatureOfBusiness_PB_NOB_HEALTH_MED:       "10",
			NatureOfBusiness_PB_NOB_HOTEL:            "11",
			NatureOfBusiness_PB_NOB_HOUSEHOLD:        "12",
			NatureOfBusiness_PB_NOB_IT:               "14",
			NatureOfBusiness_PB_NOB_INSURANCE:        "08",
			NatureOfBusiness_PB_NOB_LEGAL:            "15",
			NatureOfBusiness_PB_NOB_MKT_ADVERTISING:  "21",
			NatureOfBusiness_PB_NOB_NON_PROFIT:       "23",
			NatureOfBusiness_PB_NOB_OIL_GAS:          "16",
			NatureOfBusiness_PB_NOB_PROD_MANUF:       "13",
			NatureOfBusiness_PB_NOB_REPAIR_SVC:       "18",
			NatureOfBusiness_PB_NOB_RESTAURANT_FNB:   "32",
			NatureOfBusiness_PB_NOB_RETAIL_WHOLESALE: "19",
			NatureOfBusiness_PB_NOB_SPORTS:           "25",
			NatureOfBusiness_PB_NOB_TOBACCO:          "20",
			NatureOfBusiness_PB_NOB_TRANSPORT_SVC:    "03",
			NatureOfBusiness_PB_NOB_TRAVEL_TOURISM:   "09",
			NatureOfBusiness_PB_NOB_UTILITY:          "22",
			NatureOfBusiness_PB_NOB_OTHERS:           "27",
		}
		if val, ok := publicEmployment[n]; ok {
			return val, nil
		}
	//nolint:dupl
	case EmploymentDetailsType_PRIVATE_SECTOR_EMPLOYEE:
		privateEmployment := map[NatureOfBusiness]string{
			NatureOfBusiness_PV_NOB_AGRIC_FOREST:     "01",
			NatureOfBusiness_PV_NOB_BUSINESS:         "04",
			NatureOfBusiness_PV_NOB_CHEMICAL:         "24",
			NatureOfBusiness_PV_NOB_CONST_REAL_EST:   "05",
			NatureOfBusiness_PV_NOB_GOV_OTHERS:       "28",
			NatureOfBusiness_PV_NOB_GOV_DEFENSE:      "29",
			NatureOfBusiness_PV_NOB_EDUCATION:        "06",
			NatureOfBusiness_PV_NOB_ENT_OTHERS:       "30",
			NatureOfBusiness_PV_NOB_ENT_CASINO_KTV:   "31",
			NatureOfBusiness_PV_NOB_FINANCE:          "07",
			NatureOfBusiness_PV_NOB_HEALTH_MED:       "10",
			NatureOfBusiness_PV_NOB_HOTEL:            "11",
			NatureOfBusiness_PV_NOB_HOUSEHOLD:        "12",
			NatureOfBusiness_PV_NOB_IT:               "14",
			NatureOfBusiness_PV_NOB_INSURANCE:        "08",
			NatureOfBusiness_PV_NOB_LEGAL:            "15",
			NatureOfBusiness_PV_NOB_MKT_ADVERTISING:  "21",
			NatureOfBusiness_PV_NOB_NON_PROFIT:       "23",
			NatureOfBusiness_PV_NOB_OIL_GAS:          "16",
			NatureOfBusiness_PV_NOB_PROD_MANUF:       "13",
			NatureOfBusiness_PV_NOB_REPAIR_SVC:       "18",
			NatureOfBusiness_PV_NOB_RESTAURANT_FNB:   "32",
			NatureOfBusiness_PV_NOB_RETAIL_WHOLESALE: "19",
			NatureOfBusiness_PV_NOB_SPORTS:           "25",
			NatureOfBusiness_PV_NOB_TOBACCO:          "20",
			NatureOfBusiness_PV_NOB_TRANSPORT_SVC:    "03",
			NatureOfBusiness_PV_NOB_TRAVEL_TOURISM:   "09",
			NatureOfBusiness_PV_NOB_UTILITY:          "22",
			NatureOfBusiness_PV_NOB_OTHERS:           "27",
		}
		if val, ok := privateEmployment[n]; ok {
			return val, nil
		}
	//nolint:dupl
	case EmploymentDetailsType_SELF_EMPLOYED:
		selfEmployed := map[NatureOfBusiness]string{
			NatureOfBusiness_SF_NOB_AGRIC_FOREST:     "01",
			NatureOfBusiness_SF_NOB_BUSINESS:         "04",
			NatureOfBusiness_SF_NOB_CHEMICAL:         "24",
			NatureOfBusiness_SF_NOB_CONST_REAL_EST:   "05",
			NatureOfBusiness_SF_NOB_GOV_OTHERS:       "28",
			NatureOfBusiness_SF_NOB_GOV_DEFENSE:      "29",
			NatureOfBusiness_SF_NOB_EDUCATION:        "06",
			NatureOfBusiness_SF_NOB_ENT_OTHERS:       "30",
			NatureOfBusiness_SF_NOB_ENT_CASINO_KTV:   "31",
			NatureOfBusiness_SF_NOB_FINANCE:          "07",
			NatureOfBusiness_SF_NOB_HEALTH_MED:       "10",
			NatureOfBusiness_SF_NOB_HOTEL:            "11",
			NatureOfBusiness_SF_NOB_HOUSEHOLD:        "12",
			NatureOfBusiness_SF_NOB_IT:               "14",
			NatureOfBusiness_SF_NOB_INSURANCE:        "08",
			NatureOfBusiness_SF_NOB_LEGAL:            "15",
			NatureOfBusiness_SF_NOB_MKT_ADVERTISING:  "21",
			NatureOfBusiness_SF_NOB_NON_PROFIT:       "23",
			NatureOfBusiness_SF_NOB_OIL_GAS:          "16",
			NatureOfBusiness_SF_NOB_PROD_MANUF:       "13",
			NatureOfBusiness_SF_NOB_REPAIR_SVC:       "18",
			NatureOfBusiness_SF_NOB_RESTAURANT_FNB:   "32",
			NatureOfBusiness_SF_NOB_RETAIL_WHOLESALE: "19",
			NatureOfBusiness_SF_NOB_SPORTS:           "25",
			NatureOfBusiness_SF_NOB_TOBACCO:          "20",
			NatureOfBusiness_SF_NOB_TRANSPORT_SVC:    "03",
			NatureOfBusiness_SF_NOB_TRAVEL_TOURISM:   "09",
			NatureOfBusiness_SF_NOB_UTILITY:          "22",
			NatureOfBusiness_SF_NOB_OTHERS:           "27",
		}
		if val, ok := selfEmployed[n]; ok {
			return val, nil
		}

	default:
		return "", fmt.Errorf("unsupported employment type=%s", t)
	}

	return "", fmt.Errorf("unable to find nature of business for employment type=%s and nature of business=%s", t, n)
}

// convertOccupation maps values like SF_OCC_RISK_MGMT_EXEC from api to B11 from application-service
// this mapping is maintained manually, and could be prone to being outdated
func convertOccupation(t EmploymentDetailsType, o Occupation) (string, error) {
	switch t {
	//nolint:dupl
	case EmploymentDetailsType_PUBLIC_SECTOR_EMPLOYEE:
		publicEmployment := map[Occupation]string{
			Occupation_PB_OCC_ARMED_FORCES:    "A01",
			Occupation_PB_OCC_CIVIL_DEF:       "A02",
			Occupation_PB_OCC_CUSTOMS_BORDER:  "A03",
			Occupation_PB_OCC_FIREFIGHTERS:    "A04",
			Occupation_PB_OCC_IMMIG_CUSTOMS:   "A05",
			Occupation_PB_OCC_PRISON_GUARDS:   "A06",
			Occupation_PB_OCC_POLICE_OFFICERS: "A07",
			Occupation_PB_OCC_POLICE_INSP:     "A08",
			Occupation_PB_OCC_REG_GOV:         "A09",
			Occupation_PB_OCC_TAX_EXCISE:      "A10",
			Occupation_PB_OCC_OTHER_GOV:       "A11",
		}
		if val, ok := publicEmployment[o]; ok {
			return val, nil
		}
	//nolint:dupl
	case EmploymentDetailsType_PRIVATE_SECTOR_EMPLOYEE:
		privateEmployment := map[Occupation]string{
			Occupation_PV_OCC_LEGISLATORS:      "B01",
			Occupation_PV_OCC_MD_CEO:           "B02",
			Occupation_PV_OCC_SALES_MKT_EXEC:   "B03",
			Occupation_PV_OCC_BIZ_DEV_EXEC:     "B04",
			Occupation_PV_OCC_PROJ_EXEC:        "B05",
			Occupation_PV_OCC_PROD_MGMT_EXEC:   "B06",
			Occupation_PV_OCC_CUST_OPS_EXEC:    "B07",
			Occupation_PV_OCC_HR_EXEC:          "B08",
			Occupation_PV_OCC_FIN_EXEC:         "B09",
			Occupation_PV_OCC_IT_EXEC:          "B10",
			Occupation_PV_OCC_RISK_MGMT_EXEC:   "B11",
			Occupation_PV_OCC_LEGAL_EXEC:       "B12",
			Occupation_PV_OCC_SCIENTIST:        "B13",
			Occupation_PV_OCC_PLANNERS:         "B14",
			Occupation_PV_OCC_ADMIN_CLERKS:     "B15",
			Occupation_PV_OCC_CHEF_COOKS:       "B16",
			Occupation_PV_OCC_CHEMISTS:         "B17",
			Occupation_PV_OCC_MATH_ACT_STAT:    "B18",
			Occupation_PV_OCC_FARM_FOREST_FISH: "B19",
			Occupation_PV_OCC_ENGINEER:         "B20",
			Occupation_PV_OCC_ARCHITECTS:       "B21",
			Occupation_PV_OCC_DESIGNERS:        "B22",
			Occupation_PV_OCC_PILOTS:           "B23",
			Occupation_PV_OCC_DRIVERS:          "B24",
			Occupation_PV_OCC_DOCTORS:          "B25",
			Occupation_PV_OCC_NURSE_MIDWIFERY:  "B26",
			Occupation_PV_OCC_DENTISTS:         "B27",
			Occupation_PV_OCC_PHARMACISTS:      "B28",
			Occupation_PV_OCC_OPTICIAN_OPHTH:   "B29",
			Occupation_PV_OCC_TEACHERS_LECT:    "B30",
			Occupation_PV_OCC_SALES_MARKETERS:  "B31",
			Occupation_PV_OCC_ANALYST:          "B32",
			Occupation_PV_OCC_DEV_PROGRAMMERS:  "B33",
			Occupation_PV_OCC_DB_ANALYST_ADM:   "B34",
			Occupation_PV_OCC_LAWYERS_JUDGES:   "B35",
			Occupation_PV_OCC_ECONOMISTS:       "B36",
			Occupation_PV_OCC_SOC_ANTHRO_PROF:  "B37",
			Occupation_PV_OCC_PHIL_HIST_POL:    "B38",
			Occupation_PV_OCC_PSYCHOLOGISTS:    "B39",
			Occupation_PV_OCC_SOCIAL_WORK_PROF: "B40",
			Occupation_PV_OCC_RELIG_PROF:       "B41",
			Occupation_PV_OCC_AUTH_JOURN:       "B42",
			Occupation_PV_OCC_ARTIST_PERFORM:   "B43",
			Occupation_PV_OCC_MUSIC_SINGER:     "B45",
			Occupation_PV_OCC_PROD_DIR_ENT:     "B46",
			Occupation_PV_OCC_TECH_ELECTRIC:    "B47",
			Occupation_PV_OCC_SOCIAL_CULT_WKR:  "B49",
			Occupation_PV_OCC_THERAPISTS:       "B50",
			Occupation_PV_OCC_DEALERS_BROKERS:  "B51",
			Occupation_PV_OCC_AGENT_INS_COMM:   "B52",
			Occupation_PV_OCC_BUYERS_PURCH:     "B53",
			Occupation_PV_OCC_ATHLETES_SPORT:   "B54",
			Occupation_PV_OCC_COACH_TRAINER:    "B55",
			Occupation_PV_OCC_PHOTOGRAPHER:     "B56",
			Occupation_PV_OCC_SECRETARIES:      "B57",
			Occupation_PV_OCC_CONSULTANTS:      "B58",
			Occupation_PV_OCC_RECEPTIONIST:     "B59",
			Occupation_PV_OCC_ATTEND_STEWARDS:  "B60",
			Occupation_PV_OCC_WAITERS_BARTEND:  "B61",
			Occupation_PV_OCC_BEAUT_HAIR:       "B62",
			Occupation_PV_OCC_CASHIERS:         "B63",
			Occupation_PV_OCC_CHILD_CARE:       "B64",
			Occupation_PV_OCC_FACTORY_WORKER:   "B65",
			Occupation_PV_OCC_CARP_PLUMB_MECH:  "B66",
			Occupation_PV_OCC_CONST_WORKERS:    "B67",
			Occupation_PV_OCC_TAILORS_DRESS:    "B68",
			Occupation_PV_OCC_CLEAN_HELPERS:    "B69",
			Occupation_PV_OCC_GIG_FREELANCERS:  "B70",
			Occupation_PV_OCC_INVESTOR:         "B71",
			Occupation_PV_OCC_CONTENT_KOLS:     "B72",
			Occupation_PV_OCC_BIZ_OWNER:        "B73",
			Occupation_PV_OCC_OTHERS:           "B74",
		}
		if val, ok := privateEmployment[o]; ok {
			return val, nil
		}
	//nolint:dupl
	case EmploymentDetailsType_SELF_EMPLOYED:
		selfEmployed := map[Occupation]string{
			Occupation_SF_OCC_LEGISLATORS:      "B01",
			Occupation_SF_OCC_MD_CEO:           "B02",
			Occupation_SF_OCC_SALES_MKT_EXEC:   "B03",
			Occupation_SF_OCC_BIZ_DEV_EXEC:     "B04",
			Occupation_SF_OCC_PROJ_EXEC:        "B05",
			Occupation_SF_OCC_PROD_MGMT_EXEC:   "B06",
			Occupation_SF_OCC_CUST_OPS_EXEC:    "B07",
			Occupation_SF_OCC_HR_EXEC:          "B08",
			Occupation_SF_OCC_FIN_EXEC:         "B09",
			Occupation_SF_OCC_IT_EXEC:          "B10",
			Occupation_SF_OCC_RISK_MGMT_EXEC:   "B11",
			Occupation_SF_OCC_LEGAL_EXEC:       "B12",
			Occupation_SF_OCC_SCIENTIST:        "B13",
			Occupation_SF_OCC_PLANNERS:         "B14",
			Occupation_SF_OCC_ADMIN_CLERKS:     "B15",
			Occupation_SF_OCC_CHEF_COOKS:       "B16",
			Occupation_SF_OCC_CHEMISTS:         "B17",
			Occupation_SF_OCC_MATH_ACT_STAT:    "B18",
			Occupation_SF_OCC_FARM_FOREST_FISH: "B19",
			Occupation_SF_OCC_ENGINEER:         "B20",
			Occupation_SF_OCC_ARCHITECTS:       "B21",
			Occupation_SF_OCC_DESIGNERS:        "B22",
			Occupation_SF_OCC_PILOTS:           "B23",
			Occupation_SF_OCC_DRIVERS:          "B24",
			Occupation_SF_OCC_DOCTORS:          "B25",
			Occupation_SF_OCC_NURSE_MIDWIFERY:  "B26",
			Occupation_SF_OCC_DENTISTS:         "B27",
			Occupation_SF_OCC_PHARMACISTS:      "B28",
			Occupation_SF_OCC_OPTICIAN_OPHTH:   "B29",
			Occupation_SF_OCC_TEACHERS_LECT:    "B30",
			Occupation_SF_OCC_SALES_MARKETERS:  "B31",
			Occupation_SF_OCC_ANALYST:          "B32",
			Occupation_SF_OCC_DEV_PROGRAMMERS:  "B33",
			Occupation_SF_OCC_DB_ANALYST_ADM:   "B34",
			Occupation_SF_OCC_LAWYERS_JUDGES:   "B35",
			Occupation_SF_OCC_ECONOMISTS:       "B36",
			Occupation_SF_OCC_SOC_ANTHRO_PROF:  "B37",
			Occupation_SF_OCC_PHIL_HIST_POL:    "B38",
			Occupation_SF_OCC_PSYCHOLOGISTS:    "B39",
			Occupation_SF_OCC_SOCIAL_WORK_PROF: "B40",
			Occupation_SF_OCC_RELIG_PROF:       "B41",
			Occupation_SF_OCC_AUTH_JOURN:       "B42",
			Occupation_SF_OCC_ARTIST_PERFORM:   "B43",
			Occupation_SF_OCC_MUSIC_SINGER:     "B45",
			Occupation_SF_OCC_PROD_DIR_ENT:     "B46",
			Occupation_SF_OCC_TECH_ELECTRIC:    "B47",
			Occupation_SF_OCC_SOCIAL_CULT_WKR:  "B49",
			Occupation_SF_OCC_THERAPISTS:       "B50",
			Occupation_SF_OCC_DEALERS_BROKERS:  "B51",
			Occupation_SF_OCC_AGENT_INS_COMM:   "B52",
			Occupation_SF_OCC_BUYERS_PURCH:     "B53",
			Occupation_SF_OCC_ATHLETES_SPORT:   "B54",
			Occupation_SF_OCC_COACH_TRAINER:    "B55",
			Occupation_SF_OCC_PHOTOGRAPHER:     "B56",
			Occupation_SF_OCC_SECR_SECRETARIES: "B57",
			Occupation_SF_OCC_CONSULTANTS:      "B58",
			Occupation_SF_OCC_RECEPTIONIST:     "B59",
			Occupation_SF_OCC_ATTEND_STEWARDS:  "B60",
			Occupation_SF_OCC_WAITERS_BARTEND:  "B61",
			Occupation_SF_OCC_BEAUT_HAIR:       "B62",
			Occupation_SF_OCC_CASHIERS:         "B63",
			Occupation_SF_OCC_CHILD_CARE:       "B64",
			Occupation_SF_OCC_FACTORY_WORKER:   "B65",
			Occupation_SF_OCC_CARP_PLUMB_MECH:  "B66",
			Occupation_SF_OCC_CONST_WORKERS:    "B67",
			Occupation_SF_OCC_TAILORS_DRESS:    "B68",
			Occupation_SF_OCC_CLEAN_HELPERS:    "B69",
			Occupation_SF_OCC_GIG_FREELANCERS:  "B70",
			Occupation_SF_OCC_INVESTOR:         "B71",
			Occupation_SF_OCC_CONTENT_KOLS:     "B72",
			Occupation_SF_OCC_BIZ_OWNER:        "B73",
			Occupation_SF_OCC_OTHERS:           "B74",
		}
		if val, ok := selfEmployed[o]; ok {
			return val, nil
		}
	case EmploymentDetailsType_UNEMPLOYED, EmploymentDetailsType_RETIRED_PENSIONER, EmploymentDetailsType_STUDENT:
		return "", nil
	default:
		return "", fmt.Errorf("unsupported employment type=%s", t)
	}

	return "", fmt.Errorf("unable to find employment code for employment type=%s and occupation=%s", t, o)
}

func convertEmploymentType(t EmploymentDetailsType) (customermaster.EmploymentType, error) {
	switch t {
	case EmploymentDetailsType_PUBLIC_SECTOR_EMPLOYEE:
		return customermaster.EmploymentType_PublicEmployee, nil
	case EmploymentDetailsType_PRIVATE_SECTOR_EMPLOYEE:
		return customermaster.EmploymentType_PrivateEmployee, nil
	case EmploymentDetailsType_RETIRED_PENSIONER:
		return customermaster.EmploymentType_Retired, nil
	case EmploymentDetailsType_SELF_EMPLOYED:
		return customermaster.EmploymentType_SelfEmployed, nil
	case EmploymentDetailsType_STUDENT:
		return customermaster.EmploymentType_Student, nil
	case EmploymentDetailsType_UNEMPLOYED:
		return customermaster.EmploymentType_Unknown, nil
	default:
		return customermaster.EmploymentType_Unknown, fmt.Errorf("unknown employment type=%s", t)
	}
}

func convertConsent(isConsented bool) *customermaster.AgreementStatus {
	var (
		tncAgreementStatusAccepted   = customermaster.AgreementStatus_Accepted
		tncAgreementStatusUnaccepted = customermaster.AgreementStatus_Unaccepted
	)
	if isConsented {
		return &tncAgreementStatusAccepted
	}
	return &tncAgreementStatusUnaccepted
}

func convertGender(gender stringutil.Gender) (customermaster.Gender, error) {
	switch gender {
	case stringutil.Gender_Male:
		return customermaster.Gender_Male, nil
	case stringutil.Gender_Female:
		return customermaster.Gender_Female, nil
	case stringutil.Gender_Unknown:
		return customermaster.Gender_Unknown, nil
	default:
		return customermaster.Gender_Unknown, fmt.Errorf("unknown gender: %s", gender)
	}
}
