package applicationSubmission

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"gitlab.com/gx-regional/dakota/common/aws/s3client"
	"gitlab.com/gx-regional/dakota/workflowengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/hermes"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/onboarding/customermaster"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/server/config"

	"gitlab.com/gx-regional/dakota/klient"

	"github.com/go-resty/resty/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func Test_loanDocumentDestinationFolderPath(t *testing.T) {
	type args struct {
		data     *ExecutionData
		document SupportingDocumentMetadata
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "happy path",
			args: args{
				data: &ExecutionData{
					CreateLoanApplicationRequest: &CreateLoanApplicationRequest{
						ReferenceID: "123456",
					},
				},
				document: SupportingDocumentMetadata{
					DocumentType: SupportingDocumentType_EPF_STATEMENT,
				},
			},
			want: "partner/tng/lendingIncomeEstimation/123456/EPF_STATEMENT",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, loanDocumentDestinationFolderPath(tt.args.data, tt.args.document), "loanDocumentDestinationFolderPath(%v, %v)", tt.args.data, tt.args.document)
		})
	}
}

// local integration test
func TestWorkflowImpl_local_uploadLoanApplicationDocumentToHermes(t *testing.T) {
	if os.Getenv("TEST_S3") != "true" {
		return
	}
	s3Client, err := s3client.NewS3ClientForDevelopmentMode()
	require.NoErrorf(t, err, "error creating s3 client")
	srcBucket := "dbmy-dev-backend-s3-tng-sftp"
	destBucket := "dev-backend-loan-app-dbmy-s3"
	hermesClient, err := hermes.NewDbmyClientWithOptions(&external.ServiceConfig{
		BaseURL:   "https://backend.dev.g-bank.app/hermes",
		GroupName: "dbmy",
	}, klient.PropagateCommonHeaders())
	require.NoErrorf(t, err, "error creating hermes client")

	w := &WorkflowImpl{
		HermesClient: hermesClient,
		S3Client:     s3Client,
		Config: Config{
			DocumentSourceBucket:      srcBucket,
			DocumentDestinationBucket: destBucket,
		},
	}
	data, err := w.uploadLoanApplicationDocument(context.Background(), "", &ExecutionData{
		State:                   0,
		OnboardingApplicationID: "",
		CustomerID:              "7b1b1a3e-8f6c-4feb-8ade-3a73f4854c5e",
		CreateLoanApplicationRequest: &CreateLoanApplicationRequest{
			ReferenceID: "123456",
			Product: &LoanApplication{
				ProductVariantCode: "default_flexi_loan",
				TncAccepted:        false,
				NonFinancialInstitutionLoan: &NonFILoanAmount{
					Amount: 0,
				},
				IncomeAnalysis: &IncomeAnalysis{
					Sources: []IncomeSourceAnalysis{
						{
							SourceType: IncomeSourceAnalysis_SourceType_EPF_STATEMENT,
							Epf: &IncomeAnalysisSourceEPF{
								SupportingDocuments: []SupportingDocumentMetadata{
									{
										DocumentType: SupportingDocumentType_EPF_STATEMENT,
										FileName:     "Advanced Smart Contract Tutorial 2.pdf",
										FilePath:     "Advanced Smart Contract Tutorial 2.pdf",
										FileType:     "pdf",
									},
									{
										DocumentType: SupportingDocumentType_CTOS_REPORT,
										FileName:     "Advanced Smart Contract Tutorial 2 copy.pdf",
										FilePath:     "Advanced Smart Contract Tutorial 2 copy.pdf",
										FileType:     "pdf",
									},
								},
							},
						},
					},
				},
			},
		},
		LoanApplicationDocuments: nil,
	}, nil)
	require.NoErrorf(t, err, "error uploading document")
	jsonData, err := json.MarshalIndent(data, "", "  ")
	require.NoErrorf(t, err, "error marshalling data")
	t.Logf("data: %s", string(jsonData))
}

func TestWorkflowImpl_uploadLoanApplicationDocument(t *testing.T) {
	type fields struct {
		WorkflowRetryConfig  *config.WorkflowRetryConfig
		CustomerMasterClient customermaster.ICustomerMasterClient
		HermesClient         hermes.IHermesClient
		S3Client             s3client.S3
		Config               Config
		restClientFunc       func(string) (*resty.Response, error)
	}
	type args struct {
		ctx          context.Context
		transitionID string
		execData     workflowengine.ExecutionData
		params       interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    workflowengine.ExecutionData
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "successful upload",
			fields: fields{
				HermesClient: &hermes.MockHermesClient{
					GetUploadURLV2Func: func(ctx context.Context, req *hermes.GetUploadURLRequest) (*hermes.GetUploadURLResponse, error) {
						return &hermes.GetUploadURLResponse{
							Documents: []hermes.DocUploadResDetails{
								{
									DocumentID:       "doc-123",
									PresignedURL:     "https://example.com/upload",
									PresignedHeaders: map[string]string{"Content-Type": "application/pdf"},
								},
							},
						}, nil
					},
				},
				S3Client: &s3client.MockS3Client{
					DownloadObjectFunc: func(ctx context.Context, bucket, key string) ([]byte, error) {
						return []byte("test file content"), nil
					},
				},
				Config: Config{
					DocumentSourceBucket:      "source-bucket",
					DocumentDestinationBucket: "dest-bucket",
				},
				restClientFunc: func(url string) (*resty.Response, error) {
					return &resty.Response{}, nil
				},
			},
			args: args{
				ctx:          context.Background(),
				transitionID: "test-transition",
				execData: &ExecutionData{
					CustomerID: "customer-123",
					CreateLoanApplicationRequest: &CreateLoanApplicationRequest{
						ReferenceID: "ref-123",
						Product: &LoanApplication{
							IncomeAnalysis: &IncomeAnalysis{
								Sources: []IncomeSourceAnalysis{
									{
										SourceType: IncomeSourceAnalysis_SourceType_EPF_STATEMENT,
										Epf: &IncomeAnalysisSourceEPF{
											SupportingDocuments: []SupportingDocumentMetadata{
												{
													DocumentType: SupportingDocumentType_EPF_STATEMENT,
													FileName:     "test-file.pdf",
												}},
										},
									},
								}},
						},
					},
				},
			},
			want: &ExecutionData{
				CustomerID: "customer-123",
				CreateLoanApplicationRequest: &CreateLoanApplicationRequest{
					ReferenceID: "ref-123",
					Product: &LoanApplication{
						IncomeAnalysis: &IncomeAnalysis{
							Sources: []IncomeSourceAnalysis{
								{
									SourceType: IncomeSourceAnalysis_SourceType_EPF_STATEMENT,
									Epf: &IncomeAnalysisSourceEPF{
										SupportingDocuments: []SupportingDocumentMetadata{
											{
												DocumentType: SupportingDocumentType_EPF_STATEMENT,
												FileName:     "test-file.pdf",
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: assert.NoError,
		},
	},
},
for _, tt := range tests{
t.Run(tt.name, func (t *testing.T){
w := &WorkflowImpl{
WorkflowRetryConfig:  tt.fields.WorkflowRetryConfig,
CustomerMasterClient: tt.fields.CustomerMasterClient,
HermesClient:         tt.fields.HermesClient,
S3Client:             tt.fields.S3Client,
Config:               tt.fields.Config,
restClientFunc:       tt.fields.restClientFunc,
}
got, err := w.uploadLoanApplicationDocument(tt.args.ctx, tt.args.transitionID, tt.args.execData, tt.args.params)
if !tt.wantErr(t, err, fmt.Sprintf("uploadLoanApplicationDocument(%v, %v, %v, %v)", tt.args.ctx, tt.args.transitionID, tt.args.execData, tt.args.params)){
return
}
assert.Equalf(t, tt.want, got, "uploadLoanApplicationDocument(%v, %v, %v, %v)", tt.args.ctx, tt.args.transitionID, tt.args.execData, tt.args.params)
})
}
}
