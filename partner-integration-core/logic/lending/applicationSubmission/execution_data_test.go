package applicationSubmission

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/workflowengine"
)

func TestExecutionData_GetState(t *testing.T) {
	tests := []struct {
		name          string
		executionData *ExecutionData
		expectedState workflowengine.State
	}{
		{
			name: "should return the current state when state is set",
			executionData: &ExecutionData{
				State: stInit,
			},
			expectedState: stInit,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.executionData.GetState()
			if result != tt.expectedState {
				t.Errorf("GetState() = %v, want %v", result, tt.expectedState)
			}
		})
	}
}

func TestExecutionData_SetState(t *testing.T) {
	tests := []struct {
		name         string
		initialState workflowengine.State
		newState     workflowengine.State
		want         workflowengine.State
	}{
		{
			name:         "set state from one valid state to another",
			initialState: stInit,
			newState:     stCreateEcosystemId,
			want:         stCreateEcosystemId,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &ExecutionData{
				State: tt.initialState,
			}

			e.SetState(tt.newState)

			if e.State != tt.want {
				t.Errorf("SetState() = %v, want %v", e.State, tt.want)
			}
		})
	}
}

func TestExecutionData_SetState_Integration(t *testing.T) {
	// Test that SetState works correctly with other methods
	e := &ExecutionData{}

	// Set initial state
	e.SetState(stInit)

	// Verify GetState returns the set state
	if got := e.GetState(); got != stInit {
		t.Errorf("GetState() after SetState() = %v, want %v", got, "INITIAL")
	}

	// Change state
	e.SetState(stInit)

	// Verify the state was updated
	if got := e.GetState(); got != stInit {
		t.Errorf("GetState() after second SetState() = %v, want %v", got, "UPDATED")
	}
}

func TestExecutionData_SetState_Clone(t *testing.T) {
	// Test that SetState on original doesn't affect cloned instance
	original := &ExecutionData{State: stInit}
	cloned := original.Clone()

	// Modify original
	original.SetState(stInit)

	// Verify clone is unchanged
	if cloned.State != stInit {
		t.Errorf("Cloned state = %v, want %v", cloned.State, "ORIGINAL")
	}

	// Verify original is changed
	if original.State != stInit {
		t.Errorf("Original state = %v, want %v", original.State, "MODIFIED")
	}
}

func TestExecutionData_Marshal(t *testing.T) {
	tests := []struct {
		name     string
		data     *ExecutionData
		wantErr  bool
		validate func(t *testing.T, result []byte)
	}{
		{
			name: "marshal with valid state",
			data: &ExecutionData{
				State: stInit,
			},
			wantErr: false,
			validate: func(t *testing.T, result []byte) {
				var unmarshaled ExecutionData
				err := json.Unmarshal(result, &unmarshaled)
				require.NoError(t, err)
				assert.Equal(t, stInit, unmarshaled.State)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.data.Marshal()

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// Validate the result is valid JSON
				assert.True(t, json.Valid(result))

				if tt.validate != nil {
					tt.validate(t, result)
				}
			}
		})
	}
}

func TestExecutionData_Unmarshal(t *testing.T) {
	tests := []struct {
		name     string
		input    []byte
		initial  *ExecutionData
		wantErr  bool
		expected *ExecutionData
	}{
		{
			name:    "unmarshal valid JSON with state",
			input:   []byte(`{"State":100}`),
			initial: &ExecutionData{},
			wantErr: false,
			expected: &ExecutionData{
				State: stCreateEcosystemId,
			},
		},

		{
			name:    "unmarshal overwrites existing state",
			input:   []byte(`{"State":100}`),
			initial: &ExecutionData{State: stCreateEcosystemId},
			wantErr: false,
			expected: &ExecutionData{
				State: stCreateEcosystemId,
			},
		},
		{
			name:     "unmarshal invalid JSON",
			input:    []byte(`{"State":"INVALID"`),
			initial:  &ExecutionData{},
			wantErr:  true,
			expected: &ExecutionData{}, // Should remain unchanged on error
		},
		{
			name:     "unmarshal empty byte slice",
			input:    []byte(``),
			initial:  &ExecutionData{},
			wantErr:  true,
			expected: &ExecutionData{}, // Should remain unchanged on error
		},
		{
			name:    "unmarshal with extra fields (should be ignored)",
			input:   []byte(`{"State":100,"ExtraField":"ignored"}`),
			initial: &ExecutionData{},
			wantErr: false,
			expected: &ExecutionData{
				State: stCreateEcosystemId,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.initial.Unmarshal(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected.State, tt.initial.State)
			}
		})
	}
}

func TestExecutionData_MarshalUnmarshalRoundTrip(t *testing.T) {
	tests := []struct {
		name string
		data *ExecutionData
	}{
		{
			name: "round trip with state",
			data: &ExecutionData{
				State: stInit,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Marshal the original data
			marshaled, err := tt.data.Marshal()
			require.NoError(t, err)

			// Unmarshal into a new instance
			var unmarshaled ExecutionData
			err = unmarshaled.Unmarshal(marshaled)
			require.NoError(t, err)

			// Verify they are equal
			assert.Equal(t, tt.data.State, unmarshaled.State)

			// Marshal again to ensure consistency
			remarshaled, err := unmarshaled.Marshal()
			require.NoError(t, err)

			// The JSON should be identical
			assert.JSONEq(t, string(marshaled), string(remarshaled))
		})
	}
}

func TestExecutionData_MarshalNilReceiver(t *testing.T) {
	var e *ExecutionData

	// This should panic or return an error depending on json.Marshal behavior
	// Testing the actual behavior
	result, err := e.Marshal()

	// json.Marshal on nil pointer should return "null"
	assert.NoError(t, err)
	assert.Equal(t, []byte("null"), result)
}
