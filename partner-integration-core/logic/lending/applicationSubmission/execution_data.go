package applicationSubmission

import (
	"encoding/json"

	we "gitlab.com/gx-regional/dakota/workflowengine"
)

// ExecutionData is the state machine internal representation
type ExecutionData struct {
	State we.State

	// application id from onboarding's application-service. Expected to be passed in during the initiation of workflow.
	OnboardingApplicationID string `json:"onboarding_application_id"`

	// customer id from create shell user. Expeccted to be passed in during the initiation of the workflow
	CustomerID string `json:"customer_id"`

	// req from createLoanApplication api. Expected to be passed in during the initiation of the workflow
	CreateLoanApplicationRequest *CreateLoanApplicationRequest   `json:"create_loan_application_request"`
	CustomerDocuments            map[string]DocumentUploadStatus `json:"customer_documents"`
	LoanApplicationDocuments     map[string]DocumentUploadStatus `json:"loan_application_documents"`
}

type CreateLoanApplicationRequest struct {
	ReferenceID string           `json:"referenceID,omitempty" validate:"string,required"`
	Customer    *Customer        `json:"customer,omitempty" validate:"ptr,required,error_msg='customer is a mandatory field.'"`
	Product     *LoanApplication `json:"product,omitempty" validate:"ptr,required,error_msg='product is a mandatory field.'"`
}

type Customer struct {
	PersonalDetails    *PersonalDetails    `json:"personalDetails,omitempty" validate:"ptr,required,error_msg='personalDetails is a mandatory field.'"`
	ContactInformation *ContactInformation `json:"contactInformation,omitempty" validate:"ptr,required,error_msg='contactInformation is a mandatory field.'"`
	Addresses          *AddressBlock       `json:"addresses,omitempty" validate:"ptr,required,error_msg='addresses is a mandatory field.'"`
	EmploymentDetails  *EmploymentDetails  `json:"employmentDetails,omitempty" validate:"ptr,required,error_msg='employmentDetails is a mandatory field.'"`
	Documents          []DocumentMetadata  `json:"documents,omitempty" validate:"slice,min=1,error_msg='documents is a mandatory field.'"`
	Consents           *ConsentBlock       `json:"consents,omitempty" validate:"ptr,required,error_msg='consents is a mandatory field.'"`
}

type PersonalDetails struct {
	Name               string            `json:"name,omitempty" validate:"string,required"`
	IdentityDocument   *IdentityDocument `json:"identityDocument,omitempty" validate:"ptr,required,error_msg='identityDocument is a mandatory field.'"`
	DateOfBirth        string            `json:"dateOfBirth,omitempty" validate:"regex,pattern=^[0-9]{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$"`
	Nationality        string            `json:"nationality,omitempty" validate:"string,min=3,max=3,required"`
	ResidencyStatus    ResidencyStatus   `json:"residencyStatus,omitempty"`
	BumiputeraStatus   BumiputeraStatus  `json:"bumiputeraStatus,omitempty"`
	IsNonUSTaxResident bool              `json:"isNonUSTaxResident,omitempty"`
}

type DocumentUploadStatus struct {
	FileName   string `json:"fileName,omitempty" validate:"string,required"`
	FilePath   string `json:"filePath,omitempty" validate:"string,required"`
	DocumentID string `json:"documentID,omitempty" validate:"string,required"`
	Status     string `json:"status,omitempty" validate:"string,required"`
}

type ResidencyStatus string

const (
	ResidencyStatus_R  ResidencyStatus = "R"
	ResidencyStatus_NR ResidencyStatus = "NR"
)

type BumiputeraStatus string

const (
	BumiputeraStatus_BUMIPUTERA     BumiputeraStatus = "BUMIPUTERA"
	BumiputeraStatus_NON_BUMIPUTERA BumiputeraStatus = "NON_BUMIPUTERA"
)

type IdentityDocument struct {
	Type  IdentityDocumentType `json:"type,omitempty"`
	Value string               `json:"value,omitempty" validate:"string,required"`
}

type IdentityDocumentType string

const (
	IdentityDocumentType_MYKAD IdentityDocumentType = "MYKAD"
)

type ContactInformation struct {
	Email         *ContactMethod `json:"email,omitempty" validate:"ptr,required,error_msg='email is a mandatory field.'"`
	ContactNumber *ContactMethod `json:"contactNumber,omitempty" validate:"ptr,required,error_msg='contactNumber is a mandatory field.'"`
}

type ContactMethod struct {
	Value      string `json:"value,omitempty" validate:"string,required"`
	IsVerified bool   `json:"isVerified,omitempty"`
}

type AddressBlock struct {
	Registered *Address `json:"registered,omitempty" validate:"ptr,required,error_msg='registered is a mandatory field.'"`
	Mailing    *Address `json:"mailing,omitempty" validate:"ptr,required,error_msg='mailing is a mandatory field.'"`
}

type Address struct {
	Line1    string `json:"line1,omitempty" validate:"string,required"`
	Line2    string `json:"line2,omitempty" validate:"string,required"`
	Town     string `json:"town,omitempty" validate:"string,required"`
	State    string `json:"state,omitempty" validate:"string,required"`
	Postcode string `json:"postcode,omitempty" validate:"string,required"`
	Country  string `json:"country,omitempty" validate:"string,min=3,max=3,required"`
}

type EmploymentDetails struct {
	Type             EmploymentDetailsType   `json:"type,omitempty"`
	Occupation       Occupation              `json:"occupation,omitempty"`
	EmployerName     string                  `json:"employerName,omitempty" validate:"string,required"`
	NatureOfBusiness NatureOfBusiness        `json:"natureOfBusiness,omitempty"`
	MonthlyIncome    EmploymentMonthlyIncome `json:"monthlyIncome,omitempty"`
}

type EmploymentMonthlyIncome string

const (
	EmploymentMonthlyIncome_BELOW_2000  EmploymentMonthlyIncome = "BELOW_2000"
	EmploymentMonthlyIncome_2000_2999   EmploymentMonthlyIncome = "2000_2999"
	EmploymentMonthlyIncome_3000_4999   EmploymentMonthlyIncome = "3000_4999"
	EmploymentMonthlyIncome_5000_6999   EmploymentMonthlyIncome = "5000_6999"
	EmploymentMonthlyIncome_7000_8999   EmploymentMonthlyIncome = "7000_8999"
	EmploymentMonthlyIncome_9000_10999  EmploymentMonthlyIncome = "9000_10999"
	EmploymentMonthlyIncome_11000_14999 EmploymentMonthlyIncome = "11000_14999"
	EmploymentMonthlyIncome_15000_24999 EmploymentMonthlyIncome = "15000_24999"
	EmploymentMonthlyIncome_ABOVE_25000 EmploymentMonthlyIncome = "ABOVE_25000"
)

type NatureOfBusiness string

// nolint:gosec, goconst
const (
	NatureOfBusiness_PB_NOB_AGRIC_FOREST     NatureOfBusiness = "PB_NOB_AGRIC_FOREST"
	NatureOfBusiness_PB_NOB_BUSINESS         NatureOfBusiness = "PB_NOB_BUSINESS"
	NatureOfBusiness_PB_NOB_CHEMICAL         NatureOfBusiness = "PB_NOB_CHEMICAL"
	NatureOfBusiness_PB_NOB_CONST_REAL_EST   NatureOfBusiness = "PB_NOB_CONST_REAL_EST"
	NatureOfBusiness_PB_NOB_GOV_OTHERS       NatureOfBusiness = "PB_NOB_GOV_OTHERS"
	NatureOfBusiness_PB_NOB_GOV_DEFENSE      NatureOfBusiness = "PB_NOB_GOV_DEFENSE"
	NatureOfBusiness_PB_NOB_EDUCATION        NatureOfBusiness = "PB_NOB_EDUCATION"
	NatureOfBusiness_PB_NOB_ENT_OTHERS       NatureOfBusiness = "PB_NOB_ENT_OTHERS"
	NatureOfBusiness_PB_NOB_ENT_CASINO_KTV   NatureOfBusiness = "PB_NOB_ENT_CASINO_KTV"
	NatureOfBusiness_PB_NOB_FINANCE          NatureOfBusiness = "PB_NOB_FINANCE"
	NatureOfBusiness_PB_NOB_HEALTH_MED       NatureOfBusiness = "PB_NOB_HEALTH_MED"
	NatureOfBusiness_PB_NOB_HOTEL            NatureOfBusiness = "PB_NOB_HOTEL"
	NatureOfBusiness_PB_NOB_HOUSEHOLD        NatureOfBusiness = "PB_NOB_HOUSEHOLD"
	NatureOfBusiness_PB_NOB_IT               NatureOfBusiness = "PB_NOB_IT"
	NatureOfBusiness_PB_NOB_INSURANCE        NatureOfBusiness = "PB_NOB_INSURANCE"
	NatureOfBusiness_PB_NOB_LEGAL            NatureOfBusiness = "PB_NOB_LEGAL"
	NatureOfBusiness_PB_NOB_MKT_ADVERTISING  NatureOfBusiness = "PB_NOB_MKT_ADVERTISING"
	NatureOfBusiness_PB_NOB_NON_PROFIT       NatureOfBusiness = "PB_NOB_NON_PROFIT"
	NatureOfBusiness_PB_NOB_OIL_GAS          NatureOfBusiness = "PB_NOB_OIL_GAS"
	NatureOfBusiness_PB_NOB_PROD_MANUF       NatureOfBusiness = "PB_NOB_PROD_MANUF"
	NatureOfBusiness_PB_NOB_REPAIR_SVC       NatureOfBusiness = "PB_NOB_REPAIR_SVC"
	NatureOfBusiness_PB_NOB_RESTAURANT_FNB   NatureOfBusiness = "PB_NOB_RESTAURANT_FNB"
	NatureOfBusiness_PB_NOB_RETAIL_WHOLESALE NatureOfBusiness = "PB_NOB_RETAIL_WHOLESALE"
	NatureOfBusiness_PB_NOB_SPORTS           NatureOfBusiness = "PB_NOB_SPORTS"
	NatureOfBusiness_PB_NOB_TOBACCO          NatureOfBusiness = "PB_NOB_TOBACCO"
	NatureOfBusiness_PB_NOB_TRANSPORT_SVC    NatureOfBusiness = "PB_NOB_TRANSPORT_SVC"
	NatureOfBusiness_PB_NOB_TRAVEL_TOURISM   NatureOfBusiness = "PB_NOB_TRAVEL_TOURISM"
	NatureOfBusiness_PB_NOB_UTILITY          NatureOfBusiness = "PB_NOB_UTILITY"
	NatureOfBusiness_PB_NOB_OTHERS           NatureOfBusiness = "PB_NOB_OTHERS"
	NatureOfBusiness_PV_NOB_AGRIC_FOREST     NatureOfBusiness = "PV_NOB_AGRIC_FOREST"
	NatureOfBusiness_PV_NOB_BUSINESS         NatureOfBusiness = "PV_NOB_BUSINESS"
	NatureOfBusiness_PV_NOB_CHEMICAL         NatureOfBusiness = "PV_NOB_CHEMICAL"
	NatureOfBusiness_PV_NOB_CONST_REAL_EST   NatureOfBusiness = "PV_NOB_CONST_REAL_EST"
	NatureOfBusiness_PV_NOB_GOV_OTHERS       NatureOfBusiness = "PV_NOB_GOV_OTHERS"
	NatureOfBusiness_PV_NOB_GOV_DEFENSE      NatureOfBusiness = "PV_NOB_GOV_DEFENSE"
	NatureOfBusiness_PV_NOB_EDUCATION        NatureOfBusiness = "PV_NOB_EDUCATION"
	NatureOfBusiness_PV_NOB_ENT_OTHERS       NatureOfBusiness = "PV_NOB_ENT_OTHERS"
	NatureOfBusiness_PV_NOB_ENT_CASINO_KTV   NatureOfBusiness = "PV_NOB_ENT_CASINO_KTV"
	NatureOfBusiness_PV_NOB_FINANCE          NatureOfBusiness = "PV_NOB_FINANCE"
	NatureOfBusiness_PV_NOB_HEALTH_MED       NatureOfBusiness = "PV_NOB_HEALTH_MED"
	NatureOfBusiness_PV_NOB_HOTEL            NatureOfBusiness = "PV_NOB_HOTEL"
	NatureOfBusiness_PV_NOB_HOUSEHOLD        NatureOfBusiness = "PV_NOB_HOUSEHOLD"
	NatureOfBusiness_PV_NOB_IT               NatureOfBusiness = "PV_NOB_IT"
	NatureOfBusiness_PV_NOB_INSURANCE        NatureOfBusiness = "PV_NOB_INSURANCE"
	NatureOfBusiness_PV_NOB_LEGAL            NatureOfBusiness = "PV_NOB_LEGAL"
	NatureOfBusiness_PV_NOB_MKT_ADVERTISING  NatureOfBusiness = "PV_NOB_MKT_ADVERTISING"
	NatureOfBusiness_PV_NOB_NON_PROFIT       NatureOfBusiness = "PV_NOB_NON_PROFIT"
	NatureOfBusiness_PV_NOB_OIL_GAS          NatureOfBusiness = "PV_NOB_OIL_GAS"
	NatureOfBusiness_PV_NOB_PROD_MANUF       NatureOfBusiness = "PV_NOB_PROD_MANUF"
	NatureOfBusiness_PV_NOB_REPAIR_SVC       NatureOfBusiness = "PV_NOB_REPAIR_SVC"
	NatureOfBusiness_PV_NOB_RESTAURANT_FNB   NatureOfBusiness = "PV_NOB_RESTAURANT_FNB"
	NatureOfBusiness_PV_NOB_RETAIL_WHOLESALE NatureOfBusiness = "PV_NOB_RETAIL_WHOLESALE"
	NatureOfBusiness_PV_NOB_SPORTS           NatureOfBusiness = "PV_NOB_SPORTS"
	NatureOfBusiness_PV_NOB_TOBACCO          NatureOfBusiness = "PV_NOB_TOBACCO"
	NatureOfBusiness_PV_NOB_TRANSPORT_SVC    NatureOfBusiness = "PV_NOB_TRANSPORT_SVC"
	NatureOfBusiness_PV_NOB_TRAVEL_TOURISM   NatureOfBusiness = "PV_NOB_TRAVEL_TOURISM"
	NatureOfBusiness_PV_NOB_UTILITY          NatureOfBusiness = "PV_NOB_UTILITY"
	NatureOfBusiness_PV_NOB_OTHERS           NatureOfBusiness = "PV_NOB_OTHERS"
	NatureOfBusiness_SF_NOB_AGRIC_FOREST     NatureOfBusiness = "SF_NOB_AGRIC_FOREST"
	NatureOfBusiness_SF_NOB_BUSINESS         NatureOfBusiness = "SF_NOB_BUSINESS"
	NatureOfBusiness_SF_NOB_CHEMICAL         NatureOfBusiness = "SF_NOB_CHEMICAL"
	NatureOfBusiness_SF_NOB_CONST_REAL_EST   NatureOfBusiness = "SF_NOB_CONST_REAL_EST"
	NatureOfBusiness_SF_NOB_GOV_OTHERS       NatureOfBusiness = "SF_NOB_GOV_OTHERS"
	NatureOfBusiness_SF_NOB_GOV_DEFENSE      NatureOfBusiness = "SF_NOB_GOV_DEFENSE"
	NatureOfBusiness_SF_NOB_EDUCATION        NatureOfBusiness = "SF_NOB_EDUCATION"
	NatureOfBusiness_SF_NOB_ENT_OTHERS       NatureOfBusiness = "SF_NOB_ENT_OTHERS"
	NatureOfBusiness_SF_NOB_ENT_CASINO_KTV   NatureOfBusiness = "SF_NOB_ENT_CASINO_KTV"
	NatureOfBusiness_SF_NOB_FINANCE          NatureOfBusiness = "SF_NOB_FINANCE"
	NatureOfBusiness_SF_NOB_HEALTH_MED       NatureOfBusiness = "SF_NOB_HEALTH_MED"
	NatureOfBusiness_SF_NOB_HOTEL            NatureOfBusiness = "SF_NOB_HOTEL"
	NatureOfBusiness_SF_NOB_HOUSEHOLD        NatureOfBusiness = "SF_NOB_HOUSEHOLD"
	NatureOfBusiness_SF_NOB_IT               NatureOfBusiness = "SF_NOB_IT"
	NatureOfBusiness_SF_NOB_INSURANCE        NatureOfBusiness = "SF_NOB_INSURANCE"
	NatureOfBusiness_SF_NOB_LEGAL            NatureOfBusiness = "SF_NOB_LEGAL"
	NatureOfBusiness_SF_NOB_MKT_ADVERTISING  NatureOfBusiness = "SF_NOB_MKT_ADVERTISING"
	NatureOfBusiness_SF_NOB_NON_PROFIT       NatureOfBusiness = "SF_NOB_NON_PROFIT"
	NatureOfBusiness_SF_NOB_OIL_GAS          NatureOfBusiness = "SF_NOB_OIL_GAS"
	NatureOfBusiness_SF_NOB_PROD_MANUF       NatureOfBusiness = "SF_NOB_PROD_MANUF"
	NatureOfBusiness_SF_NOB_REPAIR_SVC       NatureOfBusiness = "SF_NOB_REPAIR_SVC"
	NatureOfBusiness_SF_NOB_RESTAURANT_FNB   NatureOfBusiness = "SF_NOB_RESTAURANT_FNB"
	NatureOfBusiness_SF_NOB_RETAIL_WHOLESALE NatureOfBusiness = "SF_NOB_RETAIL_WHOLESALE"
	NatureOfBusiness_SF_NOB_SPORTS           NatureOfBusiness = "SF_NOB_SPORTS"
	NatureOfBusiness_SF_NOB_TOBACCO          NatureOfBusiness = "SF_NOB_TOBACCO"
	NatureOfBusiness_SF_NOB_TRANSPORT_SVC    NatureOfBusiness = "SF_NOB_TRANSPORT_SVC"
	NatureOfBusiness_SF_NOB_TRAVEL_TOURISM   NatureOfBusiness = "SF_NOB_TRAVEL_TOURISM"
	NatureOfBusiness_SF_NOB_UTILITY          NatureOfBusiness = "SF_NOB_UTILITY"
	NatureOfBusiness_SF_NOB_OTHERS           NatureOfBusiness = "SF_NOB_OTHERS"
)

type Occupation string

// nolint:gosec, goconst
const (
	Occupation_PB_OCC_ARMED_FORCES     Occupation = "PB_OCC_ARMED_FORCES"
	Occupation_PB_OCC_CIVIL_DEF        Occupation = "PB_OCC_CIVIL_DEF"
	Occupation_PB_OCC_CUSTOMS_BORDER   Occupation = "PB_OCC_CUSTOMS_BORDER"
	Occupation_PB_OCC_FIREFIGHTERS     Occupation = "PB_OCC_FIREFIGHTERS"
	Occupation_PB_OCC_IMMIG_CUSTOMS    Occupation = "PB_OCC_IMMIG_CUSTOMS"
	Occupation_PB_OCC_POLICE_INSP      Occupation = "PB_OCC_POLICE_INSP"
	Occupation_PB_OCC_POLICE_OFFICERS  Occupation = "PB_OCC_POLICE_OFFICERS"
	Occupation_PB_OCC_PRISON_GUARDS    Occupation = "PB_OCC_PRISON_GUARDS"
	Occupation_PB_OCC_REG_GOV          Occupation = "PB_OCC_REG_GOV"
	Occupation_PB_OCC_TAX_EXCISE       Occupation = "PB_OCC_TAX_EXCISE"
	Occupation_PB_OCC_OTHER_GOV        Occupation = "PB_OCC_OTHER_GOV"
	Occupation_PV_OCC_ADMIN_CLERKS     Occupation = "PV_OCC_ADMIN_CLERKS"
	Occupation_PV_OCC_AGENT_INS_COMM   Occupation = "PV_OCC_AGENT_INS_COMM"
	Occupation_PV_OCC_ANALYST          Occupation = "PV_OCC_ANALYST"
	Occupation_PV_OCC_ARCHITECTS       Occupation = "PV_OCC_ARCHITECTS"
	Occupation_PV_OCC_ARTIST_PERFORM   Occupation = "PV_OCC_ARTIST_PERFORM"
	Occupation_PV_OCC_ATHLETES_SPORT   Occupation = "PV_OCC_ATHLETES_SPORT"
	Occupation_PV_OCC_ATTEND_STEWARDS  Occupation = "PV_OCC_ATTEND_STEWARDS"
	Occupation_PV_OCC_AUTH_JOURN       Occupation = "PV_OCC_AUTH_JOURN"
	Occupation_PV_OCC_BEAUT_HAIR       Occupation = "PV_OCC_BEAUT_HAIR"
	Occupation_PV_OCC_BIZ_DEV_EXEC     Occupation = "PV_OCC_BIZ_DEV_EXEC"
	Occupation_PV_OCC_BIZ_OWNER        Occupation = "PV_OCC_BIZ_OWNER"
	Occupation_PV_OCC_BUYERS_PURCH     Occupation = "PV_OCC_BUYERS_PURCH"
	Occupation_PV_OCC_CARP_PLUMB_MECH  Occupation = "PV_OCC_CARP_PLUMB_MECH"
	Occupation_PV_OCC_CASHIERS         Occupation = "PV_OCC_CASHIERS"
	Occupation_PV_OCC_CHEF_COOKS       Occupation = "PV_OCC_CHEF_COOKS"
	Occupation_PV_OCC_CHEMISTS         Occupation = "PV_OCC_CHEMISTS"
	Occupation_PV_OCC_CHILD_CARE       Occupation = "PV_OCC_CHILD_CARE"
	Occupation_PV_OCC_CLEAN_HELPERS    Occupation = "PV_OCC_CLEAN_HELPERS"
	Occupation_PV_OCC_COACH_TRAINER    Occupation = "PV_OCC_COACH_TRAINER"
	Occupation_PV_OCC_CONST_WORKERS    Occupation = "PV_OCC_CONST_WORKERS"
	Occupation_PV_OCC_CONSULTANTS      Occupation = "PV_OCC_CONSULTANTS"
	Occupation_PV_OCC_CONTENT_KOLS     Occupation = "PV_OCC_CONTENT_KOLS"
	Occupation_PV_OCC_CUST_OPS_EXEC    Occupation = "PV_OCC_CUST_OPS_EXEC"
	Occupation_PV_OCC_DB_ANALYST_ADM   Occupation = "PV_OCC_DB_ANALYST_ADM"
	Occupation_PV_OCC_DEALERS_BROKERS  Occupation = "PV_OCC_DEALERS_BROKERS"
	Occupation_PV_OCC_DENTISTS         Occupation = "PV_OCC_DENTISTS"
	Occupation_PV_OCC_DESIGNERS        Occupation = "PV_OCC_DESIGNERS"
	Occupation_PV_OCC_DEV_PROGRAMMERS  Occupation = "PV_OCC_DEV_PROGRAMMERS"
	Occupation_PV_OCC_DOCTORS          Occupation = "PV_OCC_DOCTORS"
	Occupation_PV_OCC_DRIVERS          Occupation = "PV_OCC_DRIVERS"
	Occupation_PV_OCC_ECONOMISTS       Occupation = "PV_OCC_ECONOMISTS"
	Occupation_PV_OCC_ENGINEER         Occupation = "PV_OCC_ENGINEER"
	Occupation_PV_OCC_FACTORY_WORKER   Occupation = "PV_OCC_FACTORY_WORKER"
	Occupation_PV_OCC_FARM_FOREST_FISH Occupation = "PV_OCC_FARM_FOREST_FISH"
	Occupation_PV_OCC_FIN_EXEC         Occupation = "PV_OCC_FIN_EXEC"
	Occupation_PV_OCC_GIG_FREELANCERS  Occupation = "PV_OCC_GIG_FREELANCERS"
	Occupation_PV_OCC_HR_EXEC          Occupation = "PV_OCC_HR_EXEC"
	Occupation_PV_OCC_IT_EXEC          Occupation = "PV_OCC_IT_EXEC"
	Occupation_PV_OCC_INVESTOR         Occupation = "PV_OCC_INVESTOR"
	Occupation_PV_OCC_LAWYERS_JUDGES   Occupation = "PV_OCC_LAWYERS_JUDGES"
	Occupation_PV_OCC_LEGAL_EXEC       Occupation = "PV_OCC_LEGAL_EXEC"
	Occupation_PV_OCC_LEGISLATORS      Occupation = "PV_OCC_LEGISLATORS"
	Occupation_PV_OCC_MD_CEO           Occupation = "PV_OCC_MD_CEO"
	Occupation_PV_OCC_MATH_ACT_STAT    Occupation = "PV_OCC_MATH_ACT_STAT"
	Occupation_PV_OCC_MUSIC_SINGER     Occupation = "PV_OCC_MUSIC_SINGER"
	Occupation_PV_OCC_NURSE_MIDWIFERY  Occupation = "PV_OCC_NURSE_MIDWIFERY"
	Occupation_PV_OCC_OPTICIAN_OPHTH   Occupation = "PV_OCC_OPTICIAN_OPHTH"
	Occupation_PV_OCC_PHARMACISTS      Occupation = "PV_OCC_PHARMACISTS"
	Occupation_PV_OCC_PHIL_HIST_POL    Occupation = "PV_OCC_PHIL_HIST_POL"
	Occupation_PV_OCC_PHOTOGRAPHER     Occupation = "PV_OCC_PHOTOGRAPHER"
	Occupation_PV_OCC_PILOTS           Occupation = "PV_OCC_PILOTS"
	Occupation_PV_OCC_PLANNERS         Occupation = "PV_OCC_PLANNERS"
	Occupation_PV_OCC_PROD_DIR_ENT     Occupation = "PV_OCC_PROD_DIR_ENT"
	Occupation_PV_OCC_PROD_MGMT_EXEC   Occupation = "PV_OCC_PROD_MGMT_EXEC"
	Occupation_PV_OCC_PROJ_EXEC        Occupation = "PV_OCC_PROJ_EXEC"
	Occupation_PV_OCC_PSYCHOLOGISTS    Occupation = "PV_OCC_PSYCHOLOGISTS"
	Occupation_PV_OCC_RECEPTIONIST     Occupation = "PV_OCC_RECEPTIONIST"
	Occupation_PV_OCC_RELIG_PROF       Occupation = "PV_OCC_RELIG_PROF"
	Occupation_PV_OCC_RISK_MGMT_EXEC   Occupation = "PV_OCC_RISK_MGMT_EXEC"
	Occupation_PV_OCC_SALES_MKT_EXEC   Occupation = "PV_OCC_SALES_MKT_EXEC"
	Occupation_PV_OCC_SALES_MARKETERS  Occupation = "PV_OCC_SALES_MARKETERS"
	Occupation_PV_OCC_SCIENTIST        Occupation = "PV_OCC_SCIENTIST"
	Occupation_PV_OCC_SECRETARIES      Occupation = "PV_OCC_SECRETARIES"
	Occupation_PV_OCC_SOCIAL_CULT_WKR  Occupation = "PV_OCC_SOCIAL_CULT_WKR"
	Occupation_PV_OCC_SOCIAL_WORK_PROF Occupation = "PV_OCC_SOCIAL_WORK_PROF"
	Occupation_PV_OCC_SOC_ANTHRO_PROF  Occupation = "PV_OCC_SOC_ANTHRO_PROF"
	Occupation_PV_OCC_TAILORS_DRESS    Occupation = "PV_OCC_TAILORS_DRESS"
	Occupation_PV_OCC_TEACHERS_LECT    Occupation = "PV_OCC_TEACHERS_LECT"
	Occupation_PV_OCC_TECH_ELECTRIC    Occupation = "PV_OCC_TECH_ELECTRIC"
	Occupation_PV_OCC_THERAPISTS       Occupation = "PV_OCC_THERAPISTS"
	Occupation_PV_OCC_WAITERS_BARTEND  Occupation = "PV_OCC_WAITERS_BARTEND"
	Occupation_PV_OCC_OTHERS           Occupation = "PV_OCC_OTHERS"
	Occupation_SF_OCC_ADMIN_CLERKS     Occupation = "SF_OCC_ADMIN_CLERKS"
	Occupation_SF_OCC_AGENT_INS_COMM   Occupation = "SF_OCC_AGENT_INS_COMM"
	Occupation_SF_OCC_ANALYST          Occupation = "SF_OCC_ANALYST"
	Occupation_SF_OCC_ARCHITECTS       Occupation = "SF_OCC_ARCHITECTS"
	Occupation_SF_OCC_ARTIST_PERFORM   Occupation = "SF_OCC_ARTIST_PERFORM"
	Occupation_SF_OCC_ATHLETES_SPORT   Occupation = "SF_OCC_ATHLETES_SPORT"
	Occupation_SF_OCC_ATTEND_STEWARDS  Occupation = "SF_OCC_ATTEND_STEWARDS"
	Occupation_SF_OCC_AUTH_JOURN       Occupation = "SF_OCC_AUTH_JOURN"
	Occupation_SF_OCC_BEAUT_HAIR       Occupation = "SF_OCC_BEAUT_HAIR"
	Occupation_SF_OCC_BIZ_DEV_EXEC     Occupation = "SF_OCC_BIZ_DEV_EXEC"
	Occupation_SF_OCC_BIZ_OWNER        Occupation = "SF_OCC_BIZ_OWNER"
	Occupation_SF_OCC_BUYERS_PURCH     Occupation = "SF_OCC_BUYERS_PURCH"
	Occupation_SF_OCC_CARP_PLUMB_MECH  Occupation = "SF_OCC_CARP_PLUMB_MECH"
	Occupation_SF_OCC_CASHIERS         Occupation = "SF_OCC_CASHIERS"
	Occupation_SF_OCC_CHEF_COOKS       Occupation = "SF_OCC_CHEF_COOKS"
	Occupation_SF_OCC_CHEMISTS         Occupation = "SF_OCC_CHEMISTS"
	Occupation_SF_OCC_CHILD_CARE       Occupation = "SF_OCC_CHILD_CARE"
	Occupation_SF_OCC_CLEAN_HELPERS    Occupation = "SF_OCC_CLEAN_HELPERS"
	Occupation_SF_OCC_COACH_TRAINER    Occupation = "SF_OCC_COACH_TRAINER"
	Occupation_SF_OCC_CONST_WORKERS    Occupation = "SF_OCC_CONST_WORKERS"
	Occupation_SF_OCC_CONSULTANTS      Occupation = "SF_OCC_CONSULTANTS"
	Occupation_SF_OCC_CONTENT_KOLS     Occupation = "SF_OCC_CONTENT_KOLS"
	Occupation_SF_OCC_CUST_OPS_EXEC    Occupation = "SF_OCC_CUST_OPS_EXEC"
	Occupation_SF_OCC_DB_ANALYST_ADM   Occupation = "SF_OCC_DB_ANALYST_ADM"
	Occupation_SF_OCC_DEALERS_BROKERS  Occupation = "SF_OCC_DEALERS_BROKERS"
	Occupation_SF_OCC_DENTISTS         Occupation = "SF_OCC_DENTISTS"
	Occupation_SF_OCC_DESIGNERS        Occupation = "SF_OCC_DESIGNERS"
	Occupation_SF_OCC_DEV_PROGRAMMERS  Occupation = "SF_OCC_DEV_PROGRAMMERS"
	Occupation_SF_OCC_DOCTORS          Occupation = "SF_OCC_DOCTORS"
	Occupation_SF_OCC_DRIVERS          Occupation = "SF_OCC_DRIVERS"
	Occupation_SF_OCC_ECONOMISTS       Occupation = "SF_OCC_ECONOMISTS"
	Occupation_SF_OCC_ENGINEER         Occupation = "SF_OCC_ENGINEER"
	Occupation_SF_OCC_FACTORY_WORKER   Occupation = "SF_OCC_FACTORY_WORKER"
	Occupation_SF_OCC_FARM_FOREST_FISH Occupation = "SF_OCC_FARM_FOREST_FISH"
	Occupation_SF_OCC_FIN_EXEC         Occupation = "SF_OCC_FIN_EXEC"
	Occupation_SF_OCC_GIG_FREELANCERS  Occupation = "SF_OCC_GIG_FREELANCERS"
	Occupation_SF_OCC_HR_EXEC          Occupation = "SF_OCC_HR_EXEC"
	Occupation_SF_OCC_IT_EXEC          Occupation = "SF_OCC_IT_EXEC"
	Occupation_SF_OCC_INVESTOR         Occupation = "SF_OCC_INVESTOR"
	Occupation_SF_OCC_LAWYERS_JUDGES   Occupation = "SF_OCC_LAWYERS_JUDGES"
	Occupation_SF_OCC_LEGAL_EXEC       Occupation = "SF_OCC_LEGAL_EXEC"
	Occupation_SF_OCC_LEGISLATORS      Occupation = "SF_OCC_LEGISLATORS"
	Occupation_SF_OCC_MD_CEO           Occupation = "SF_OCC_MD_CEO"
	Occupation_SF_OCC_MATH_ACT_STAT    Occupation = "SF_OCC_MATH_ACT_STAT"
	Occupation_SF_OCC_MUSIC_SINGER     Occupation = "SF_OCC_MUSIC_SINGER"
	Occupation_SF_OCC_NURSE_MIDWIFERY  Occupation = "SF_OCC_NURSE_MIDWIFERY"
	Occupation_SF_OCC_OPTICIAN_OPHTH   Occupation = "SF_OCC_OPTICIAN_OPHTH"
	Occupation_SF_OCC_PHARMACISTS      Occupation = "SF_OCC_PHARMACISTS"
	Occupation_SF_OCC_PHIL_HIST_POL    Occupation = "SF_OCC_PHIL_HIST_POL"
	Occupation_SF_OCC_PHOTOGRAPHER     Occupation = "SF_OCC_PHOTOGRAPHER"
	Occupation_SF_OCC_PILOTS           Occupation = "SF_OCC_PILOTS"
	Occupation_SF_OCC_PLANNERS         Occupation = "SF_OCC_PLANNERS"
	Occupation_SF_OCC_PROD_DIR_ENT     Occupation = "SF_OCC_PROD_DIR_ENT"
	Occupation_SF_OCC_PROD_MGMT_EXEC   Occupation = "SF_OCC_PROD_MGMT_EXEC"
	Occupation_SF_OCC_PROJ_EXEC        Occupation = "SF_OCC_PROJ_EXEC"
	Occupation_SF_OCC_PSYCHOLOGISTS    Occupation = "SF_OCC_PSYCHOLOGISTS"
	Occupation_SF_OCC_RECEPTIONIST     Occupation = "SF_OCC_RECEPTIONIST"
	Occupation_SF_OCC_RELIG_PROF       Occupation = "SF_OCC_RELIG_PROF"
	Occupation_SF_OCC_RISK_MGMT_EXEC   Occupation = "SF_OCC_RISK_MGMT_EXEC"
	Occupation_SF_OCC_SALES_MKT_EXEC   Occupation = "SF_OCC_SALES_MKT_EXEC"
	Occupation_SF_OCC_SALES_MARKETERS  Occupation = "SF_OCC_SALES_MARKETERS"
	Occupation_SF_OCC_SCIENTIST        Occupation = "SF_OCC_SCIENTIST"
	Occupation_SF_OCC_SECR_SECRETARIES Occupation = "SF_OCC_SECR_SECRETARIES"
	Occupation_SF_OCC_SOCIAL_CULT_WKR  Occupation = "SF_OCC_SOCIAL_CULT_WKR"
	Occupation_SF_OCC_SOCIAL_WORK_PROF Occupation = "SF_OCC_SOCIAL_WORK_PROF"
	Occupation_SF_OCC_SOC_ANTHRO_PROF  Occupation = "SF_OCC_SOC_ANTHRO_PROF"
	Occupation_SF_OCC_TAILORS_DRESS    Occupation = "SF_OCC_TAILORS_DRESS"
	Occupation_SF_OCC_TEACHERS_LECT    Occupation = "SF_OCC_TEACHERS_LECT"
	Occupation_SF_OCC_TECH_ELECTRIC    Occupation = "SF_OCC_TECH_ELECTRIC"
	Occupation_SF_OCC_THERAPISTS       Occupation = "SF_OCC_THERAPISTS"
	Occupation_SF_OCC_WAITERS_BARTEND  Occupation = "SF_OCC_WAITERS_BARTEND"
	Occupation_SF_OCC_OTHERS           Occupation = "SF_OCC_OTHERS"
	Occupation_ET_UNEMPLOYED           Occupation = "ET_UNEMPLOYED"
	Occupation_ET_RETIRED              Occupation = "ET_RETIRED"
	Occupation_ET_STUDENT              Occupation = "ET_STUDENT"
)

type DocumentMetadata struct {
	DocumentType DocumentType `json:"documentType,omitempty"`
	FileName     string       `json:"fileName,omitempty" validate:"string,required"`
	FilePath     string       `json:"filePath,omitempty" validate:"string,required"`
	FileType     FileType     `json:"fileType,omitempty"`
	Checksum     string       `json:"checksum,omitempty" validate:"string,required"`
}

type EmploymentDetailsType string

const (
	EmploymentDetailsType_PUBLIC_SECTOR_EMPLOYEE  EmploymentDetailsType = "PUBLIC_SECTOR_EMPLOYEE"
	EmploymentDetailsType_PRIVATE_SECTOR_EMPLOYEE EmploymentDetailsType = "PRIVATE_SECTOR_EMPLOYEE"
	EmploymentDetailsType_RETIRED_PENSIONER       EmploymentDetailsType = "RETIRED_PENSIONER"
	EmploymentDetailsType_SELF_EMPLOYED           EmploymentDetailsType = "SELF_EMPLOYED"
	EmploymentDetailsType_STUDENT                 EmploymentDetailsType = "STUDENT"
	EmploymentDetailsType_UNEMPLOYED              EmploymentDetailsType = "UNEMPLOYED"
)

type DocumentType string

const (
	DocumentType_NRIC_FRONT DocumentType = "NRIC_FRONT"
)

type FileType string

const (
	FileType_pdf  FileType = "pdf"
	FileType_jpeg FileType = "jpeg"
)

type ConsentBlock struct {
	AllowMarketing      bool `json:"allowMarketing,omitempty"`
	AllowCreditProducts bool `json:"allowCreditProducts,omitempty"`
	AllowDataSharing    bool `json:"allowDataSharing,omitempty"`
}

type LoanApplication struct {
	ProductVariantCode          string           `json:"productVariantCode,omitempty" validate:"string,required"`
	TncAccepted                 bool             `json:"tncAccepted,omitempty"`
	NonFinancialInstitutionLoan *NonFILoanAmount `json:"nonFinancialInstitutionLoan,omitempty" validate:"ptr,required,error_msg='nonFinancialInstitutionLoan is a mandatory field.'"`
	PurposeOfLoan               PurposeOfLoan    `json:"purposeOfLoan,omitempty"`
	IncomeAnalysis              *IncomeAnalysis  `json:"incomeAnalysis,omitempty" validate:"ptr,required,error_msg='incomeAnalysis is a mandatory field.'"`
}

type PurposeOfLoan string

const (
	PurposeOfLoan_POL_RENOVATION      PurposeOfLoan = "POL_RENOVATION"
	PurposeOfLoan_POL_HOUSEHOLD_EXP   PurposeOfLoan = "POL_HOUSEHOLD_EXP"
	PurposeOfLoan_POL_OTHERS_PERSONAL PurposeOfLoan = "POL_OTHERS_PERSONAL"
)

type NonFILoanAmount struct {
	Amount   int64  `json:"amount,omitempty" validate:"int64,min=0"`
	Currency string `json:"currency,omitempty" validate:"string,max=3,required"`
}

type IncomeAnalysis struct {
	Sources []IncomeSourceAnalysis `json:"sources,omitempty" validate:"slice,min=1,error_msg='sources is a mandatory field.'"`
}

type IncomeSourceAnalysis struct {
	SourceType IncomeSourceAnalysis_SourceType `json:"sourceType,omitempty"`
	Epf        *IncomeAnalysisSourceEPF        `json:"epf,omitempty" validate:"ptr"`
}

type IncomeSourceAnalysis_SourceType string

const (
	IncomeSourceAnalysis_SourceType_EPF_STATEMENT IncomeSourceAnalysis_SourceType = "EPF_STATEMENT"
)

type IncomeAnalysisSourceEPF struct {
	AverageIncomeBased6Months  float64                                     `json:"averageIncomeBased6Months,omitempty" validate:"float64,min=0.00"`
	AverageIncomeBased12Months float64                                     `json:"averageIncomeBased12Months,omitempty" validate:"float64,min=0.00"`
	AccountSummaries           []IncomeAnalysisSourceEPF_EPFAccountSummary `json:"accountSummaries,omitempty" validate:"slice,min=1,error_msg='accountSummaries is a mandatory field.'"`
	MonthlyIncome              *IncomeAnalysisSourceEPF_EPFMonthlyIncome   `json:"monthlyIncome,omitempty" validate:"ptr,required"`
	Alerts                     *IncomeAnalysisSourceEPF_EPFAlerts          `json:"alerts,omitempty" validate:"ptr,required"`
	SupportingDocuments        []SupportingDocumentMetadata                `json:"supportingDocuments,omitempty" validate:"slice,min=1,error_msg='supportingDocuments is a mandatory field.'"`
	Metadata                   map[string]interface{}                      `json:"metadata,omitempty"`
}

type IncomeAnalysisSourceEPF_EPFMonthlyIncome struct {
	Year                   string                                `json:"year,omitempty" validate:"string,min=4,max=4,required"`
	Month                  string                                `json:"month,omitempty" validate:"string,min=2,max=2,required"`
	EmployerContribution   *IncomeAnalysisSourceEPF_IncomeAmount `json:"employerContribution,omitempty" validate:"ptr,required"`
	EmployeeContribution   *IncomeAnalysisSourceEPF_IncomeAmount `json:"employeeContribution,omitempty" validate:"ptr,required"`
	ContributionPercentage float64                               `json:"contributionPercentage,omitempty" validate:"float64,min=0.00"`
	InputedSalary          *IncomeAnalysisSourceEPF_IncomeAmount `json:"inputedSalary,omitempty" validate:"ptr,required"`
}

type IncomeAnalysisSourceEPF_EPFAlerts struct {
	IsEmployerNumberZero                     bool `json:"isEmployerNumberZero,omitempty"`
	IsIdentificationCardNumberMismatch       bool `json:"isIdentificationCardNumberMismatch,omitempty"`
	HasNoMoreThanTwoContributionsOnSingleDay bool `json:"hasNoMoreThanTwoContributionsOnSingleDay,omitempty"`
	IsLatestMonthContributionLessThan1500    bool `json:"isLatestMonthContributionLessThan1500,omitempty"`
}

type IncomeAnalysisSourceEPF_IncomeAmount struct {
	Amount   float64 `json:"amount,omitempty" validate:"float64,min=0.00"`
	Currency string  `json:"currency,omitempty" validate:"string,min=3,max=3,required"`
}

type IncomeAnalysisSourceEPF_EPFAccountSummary struct {
	AccountType    IncomeAnalysisSourceEPF_EPFAccountSummary_AccountType `json:"accountType,omitempty"`
	OpeningBalance *IncomeAnalysisSourceEPF_IncomeAmount                 `json:"openingBalance,omitempty" validate:"ptr,required"`
	Enter          *IncomeAnalysisSourceEPF_IncomeAmount                 `json:"enter,omitempty" validate:"ptr,required"`
	Exit           *IncomeAnalysisSourceEPF_IncomeAmount                 `json:"exit,omitempty" validate:"ptr,required"`
	AnnualDividend *IncomeAnalysisSourceEPF_IncomeAmount                 `json:"annualDividend,omitempty" validate:"ptr,required"`
	CurrentBalance *IncomeAnalysisSourceEPF_IncomeAmount                 `json:"currentBalance,omitempty" validate:"ptr,required"`
}

type SupportingDocumentMetadata struct {
	DocumentType SupportingDocumentType                         `json:"documentType,omitempty"`
	FileName     string                                         `json:"fileName,omitempty" validate:"string,required"`
	FilePath     string                                         `json:"filePath,omitempty" validate:"string,required"`
	FileType     string                                         `json:"fileType,omitempty" validate:"string,required"`
	Checksum     string                                         `json:"checksum,omitempty" validate:"string,required"`
	Properties   *SupportingDocumentMetadata_DocumentProperties `json:"properties,omitempty" validate:"ptr,required"`
}

type IncomeAnalysisSourceEPF_EPFAccountSummary_AccountType string

const (
	IncomeAnalysisSourceEPF_EPFAccountSummary_AccountType_AKAUN_PERSARAAN IncomeAnalysisSourceEPF_EPFAccountSummary_AccountType = "AKAUN_PERSARAAN"
	IncomeAnalysisSourceEPF_EPFAccountSummary_AccountType_AKAUN_SEJAHTERA IncomeAnalysisSourceEPF_EPFAccountSummary_AccountType = "AKAUN_SEJAHTERA"
	IncomeAnalysisSourceEPF_EPFAccountSummary_AccountType_AKAUN_FLEKSIBEL IncomeAnalysisSourceEPF_EPFAccountSummary_AccountType = "AKAUN_FLEKSIBEL"
)

type SupportingDocumentType string

const (
	SupportingDocumentType_EPF_STATEMENT SupportingDocumentType = "EPF_STATEMENT"
	SupportingDocumentType_CTOS_REPORT   SupportingDocumentType = "CTOS_REPORT"
)

type SupportingDocumentMetadata_DocumentProperties struct {
	StatementDate   string `json:"statementDate,omitempty" validate:"regex,pattern=^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}([+-][0-9]{2}:[0-9]{2}|Z)$,error_msg='statementDate must adhere to YYYY-MM-DDTHH:mm:ss±HH:mm, e.g. 2025-09-03T17:34:50+08:00'"`
	StatementPeriod string `json:"statementPeriod,omitempty" validate:"regex,pattern=^[0-9]{4}-(0[1-9]|1[012])$,error_msg='statementPeriod must adhere to YYYY-MM, e.g. 2025-12'"`
}

// GetState gets current state of the machine.
func (e *ExecutionData) GetState() we.State {
	return e.State
}

// SetState sets the state of the machine.
func (e *ExecutionData) SetState(state we.State) {
	e.State = state
}

// Marshal marshals the state context.
func (e *ExecutionData) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

// Unmarshal unmarshals the state context.
func (e *ExecutionData) Unmarshal(byteData []byte) error {
	return json.Unmarshal(byteData, e)
}

func (e *ExecutionData) Clone() *ExecutionData {
	newCtx := *e
	return &newCtx
}
