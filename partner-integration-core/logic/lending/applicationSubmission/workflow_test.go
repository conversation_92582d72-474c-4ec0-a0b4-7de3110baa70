package applicationSubmission

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/onboarding/customermaster"

	"gitlab.com/gx-regional/dakota/workflowengine/common/uuid"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/server/config"
)

func Test_workflow(t *testing.T) {
	w := &WorkflowImpl{
		WorkflowRetryConfig: &config.WorkflowRetryConfig{
			LoanApplicationSubmission: &config.RetryOptions{
				TransactionalRetryOption: &config.RetryPolicy{},
			},
		},
	}
	w.RegisterWorkflow()
}

func Test_updateCustomerMaster(t *testing.T) {
	// convenience
	var (
		mockCustomerMaster = &customermaster.MockICustomerMasterClient{}
		w                  = &WorkflowImpl{
			WorkflowRetryConfig: &config.WorkflowRetryConfig{
				LoanApplicationSubmission: &config.RetryOptions{
					TransactionalRetryOption: &config.RetryPolicy{},
				},
			},
			CustomerMasterClient: mockCustomerMaster,
		}
	)

	t.Run("happy path, minimal fields required to pass", func(t *testing.T) {
		var (
			input = &ExecutionData{
				CreateLoanApplicationRequest: &CreateLoanApplicationRequest{
					Customer: &Customer{
						PersonalDetails: &PersonalDetails{
							Name: "",
							IdentityDocument: &IdentityDocument{
								Type:  IdentityDocumentType_MYKAD,
								Value: "001231456789",
							},
						},
						ContactInformation: &ContactInformation{
							Email: &ContactMethod{
								Value: "<EMAIL>",
							},
							ContactNumber: &ContactMethod{
								Value: "0123456789",
							},
						},
						EmploymentDetails: &EmploymentDetails{
							Type:             EmploymentDetailsType_SELF_EMPLOYED,
							Occupation:       Occupation_SF_OCC_LEGISLATORS,
							NatureOfBusiness: NatureOfBusiness_SF_NOB_AGRIC_FOREST,
							MonthlyIncome:    EmploymentMonthlyIncome_5000_6999,
						},
						Consents: &ConsentBlock{},
						Addresses: &AddressBlock{
							Registered: &Address{},
							Mailing:    &Address{},
						},
					},
					Product: &LoanApplication{
						ProductVariantCode: "",
						TncAccepted:        false,
						NonFinancialInstitutionLoan: &NonFILoanAmount{
							Amount: 0,
						},
						PurposeOfLoan:  "",
						IncomeAnalysis: nil,
					},
				},
			}
			want = stCreateEcosystemId
		)

		mockCustomerMaster.On("CreateCustomer", mock.Anything, mock.Anything).Return(&customermaster.CreateCustomerResponse{}, nil).Once()
		got, err := w.updateCustomerMaster(context.Background(), uuid.NewString(), input, nil)

		assert.Equal(t, want, got.GetState())
		assert.NoError(t, err)
	})

	t.Run("sad path", func(t *testing.T) {
		t.Run("identity type is not mykad", func(t *testing.T) {
			var (
				input = &ExecutionData{
					CreateLoanApplicationRequest: &CreateLoanApplicationRequest{
						Customer: &Customer{
							PersonalDetails: &PersonalDetails{
								Name: "",
								IdentityDocument: &IdentityDocument{
									Type: IdentityDocumentType("not mykad"),
								},
							},
						},
					},
				}
			)
			res, err := w.updateCustomerMaster(context.Background(), uuid.NewString(), input, nil)

			assert.Equal(t, input, res)
			assert.EqualError(t, err, "unexpected nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.IdentityDocument.Type to not be MYKAD")
		})
		t.Run("unable to extract dob", func(t *testing.T) {
			var (
				input = &ExecutionData{
					CreateLoanApplicationRequest: &CreateLoanApplicationRequest{
						Customer: &Customer{
							PersonalDetails: &PersonalDetails{
								Name: "",
								IdentityDocument: &IdentityDocument{
									Type:  IdentityDocumentType("not mykad"),
									Value: "invalid ic value",
								},
							},
						},
					},
				}
			)
			res, err := w.updateCustomerMaster(context.Background(), uuid.NewString(), input, nil)

			assert.Equal(t, input, res)
			assert.EqualError(t, err, "unexpected nextCtx.CreateLoanApplicationRequest.Customer.PersonalDetails.IdentityDocument.Type to not be MYKAD")
		})
	})
}

func Test_createEcosystemID(t *testing.T) {
	w := &WorkflowImpl{
		WorkflowRetryConfig: &config.WorkflowRetryConfig{
			LoanApplicationSubmission: &config.RetryOptions{
				TransactionalRetryOption: &config.RetryPolicy{},
			},
		},
	}
	_, _ = w.createEcosystemID(context.Background(), uuid.NewString(), &ExecutionData{}, nil)
}

func Test_uploadUserDocumentToHermes(t *testing.T) {
	w := &WorkflowImpl{
		WorkflowRetryConfig: &config.WorkflowRetryConfig{
			LoanApplicationSubmission: &config.RetryOptions{
				TransactionalRetryOption: &config.RetryPolicy{},
			},
		},
	}
	_, _ = w.uploadCustomerDocumentToHermes(context.Background(), uuid.NewString(), &ExecutionData{}, nil)
}
