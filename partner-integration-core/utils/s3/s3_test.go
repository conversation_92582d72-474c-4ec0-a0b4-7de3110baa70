package s3

import (
	"context"
	"os"
	"testing"

	"gitlab.com/gx-regional/dakota/common/aws/s3client"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// local integration test
func TestLocalDownloadFileFromS3(t *testing.T) {
	if os.Getenv("TEST_S3") == "true" {
		bucket := "dbmy-dev-backend-s3-tng-sftp"
		key := "test-gozilla.png"
		client, err := s3client.NewS3ClientForDevelopmentMode()
		if err != nil {
			t.Log(err.Error())
		}
		require.NoError(t, err)
		ctx := context.Background()
		_, err = DownloadFileFromS3(ctx, client, bucket, key)
		assert.NoError(t, err)
	}
}
