package s3

import (
	"bytes"
	"context"
	"fmt"
	"path/filepath"

	"gitlab.com/gx-regional/dakota/common/aws/s3client"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/samber/lo"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	// S3Client ...
	S3Client s3client.S3
)

// FileUploadOptions ...
var FileUploadOptions = []s3client.UploadOption{func(input *s3manager.UploadInput) {
	s := ServerSideEncryptionAES256
	input.ServerSideEncryption = &s
}}

const (
	// ServerSideEncryptionAES256 ...
	ServerSideEncryptionAES256 = "AES256"
)

// Init initialize s3 client
func Init(ctx context.Context) {
	if S3Client != nil {
		return
	}
	client, err := s3client.NewS3Client()
	if err != nil {
		slog.FromContext(ctx).Fatal("s3ClientInit", fmt.Sprintf("%v", err))
	}
	S3Client = client
}

// GetS3Client ...
func GetS3Client(ctx context.Context) s3client.S3 {
	if S3Client == nil {
		Init(ctx)
	}
	return S3Client
}

// GetFileListFromS3 get file list from s3
func GetFileListFromS3(ctx context.Context, client s3client.S3, bucketName, folderPath string) []s3.Object {
	response, err := client.ListObjectsWithPrefix(ctx, bucketName, folderPath, 100)
	if err != nil {
		slog.FromContext(ctx).Error("S3ListObjectsWithPrefix", fmt.Sprintf("error listing objects from s3 for %s %s", bucketName, slog.Error(err)))
		return nil
	}
	if len(response) < 1 {
		slog.FromContext(ctx).Info("S3ListObjectsWithPrefix", "no file is present in s3 bucket")
		return nil
	}
	return response
}

// MoveS3FileRaw mimics "move" by uploads the given file to destination bucket and delete the copy from the source bucket
func MoveS3FileRaw(ctx context.Context, client s3client.S3, srcBucket, string, srcKey string, destBucket string, destS3Key string, fileBytes []byte, destPathPrefix string) error {
	// upload file to dest bucket
	uploadKey := lo.Ternary(lo.IsEmpty(destPathPrefix), destS3Key, filepath.Join(destBucket, destS3Key))
	err := client.Upload(destBucket, uploadKey, bytes.NewReader(fileBytes), FileUploadOptions...)
	if err != nil {
		slog.FromContext(ctx).Error("moveS3File",
			fmt.Sprintf("error while uploading file: %s/%s, err: %s", destBucket, destS3Key, err.Error()))
		return err
	}
	slog.FromContext(ctx).Info("moveS3File", fmt.Sprintf("Moved file to: %s/%s", destBucket, uploadKey))

	// delete the file from src bucket
	err = client.DeleteObject(srcBucket, srcKey)
	if err != nil {
		slog.FromContext(ctx).Error("moveS3File",
			fmt.Sprintf("error while deleting file: %s/%s, err: %s", srcBucket, srcKey, err.Error()))
		return err
	}
	slog.FromContext(ctx).Info("moveS3File", fmt.Sprintf("deleted file: %s/%s", srcBucket, srcKey))
	return nil
}

// DownloadFileFromS3 download file from S3
func DownloadFileFromS3(ctx context.Context, client s3client.S3, bucket string, key string) ([]byte, error) {
	// download the file
	slog.FromContext(ctx).Info("downloadFileFromS3", fmt.Sprintf("Started Downloading file: %s", key))
	file, err := client.Download(bucket, key)
	if err != nil {
		slog.FromContext(ctx).Error("downloadFileFromS3",
			fmt.Sprintf("error while downloading file: %s/%s, err: %s", bucket, key, err.Error()))
		return nil, err
	}

	return file, nil
}
