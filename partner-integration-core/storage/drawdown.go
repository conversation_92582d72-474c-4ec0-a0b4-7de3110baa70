package storage

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/storage/dbtype"
)

// Drawdown ...
type Drawdown struct {
	ID                           uint64                        `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PartnerID                    string                        `sql-col:"partner_id"`
	SafeID                       string                        `sql-col:"safe_id"`
	ReferenceID                  string                        `sql-col:"reference_id"`
	LendingTransactionID         string                        `sql-col:"lending_transaction_id"`
	LoanAccountID                string                        `sql-col:"loan_account_id"`
	DestinationAccount           *dbtype.DrawdownAccountDetail `sql-col:"destination_account" sql-where:"false" data-type:"json"`
	PrincipalAmount              int64                         `sql-col:"principal_amount"`
	Currency                     string                        `sql-col:"currency"`
	TransactionDomain            string                        `sql-col:"transaction_domain"`
	TransactionType              string                        `sql-col:"transaction_type"`
	TransactionSubtype           string                        `sql-col:"transaction_subtype"`
	Status                       string                        `sql-col:"status"`
	StatusReason                 string                        `sql-col:"status_reason"`
	StatusReasonDescription      string                        `sql-col:"status_reason_description"`
	LoanName                     string                        `sql-col:"loan_name"`
	PreferredRepaymentDayOfMonth int                           `sql-col:"preferred_repayment_day_of_month"`
	LoanTenorInMonths            int                           `sql-col:"loan_tenor_in_months"`
	Metadata                     json.RawMessage               `sql-col:"metadata" sql-where:"false" data-type:"json"`
	CreatedAt                    time.Time                     `sql-col:"created_at"`
	UpdatedAt                    time.Time                     `sql-col:"updated_at"`
}

const (
	DrawdownColLendingTransactionID = "LendingTransactionID"
)
const (

	// DrawdownStatusProcessing defines processing status
	DrawdownStatusProcessing = "PROCESSING"

	// DrawdownStatusCompleted defines completed status
	DrawdownStatusCompleted = "COMPLETED"

	// DrawdownStatusFailed defines failed status
	DrawdownStatusFailed = "FAILED"

	TransactionTypeDrawdown     = "DRAWDOWN"
	TransactionDomainLending    = "LENDING"
	TransactionDomainBizLending = "BIZ_LENDING"
)
