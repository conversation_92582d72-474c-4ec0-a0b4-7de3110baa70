package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.com/gx-regional/dakota/servus/v2"
	"gitlab.com/gx-regional/dakota/servus/v2/data"
	"gitlab.com/gx-regional/dakota/servus/v2/statsd"
)

var drawdownDao = "drawdown_dao"

// GetID implements the GetID function for Entity Interface
func (impl *Drawdown) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *Drawdown) SetID(ID string) {
	// replace the logic to populate unique ID for Drawdown
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *Drawdown) NewEntity() data.Entity {
	return &Drawdown{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *Drawdown) GetTableName() string {
	return "drawdown"
}

// IDrawdownDAO is the dao interface for Drawdown
//
//go:generate mockery --name IDrawdownDAO --inpackage --case=underscore mockery --name IAccountDAO --inpackage --case=underscore --replace-type gitlab.myteksi.net/gophers/go/commons/data=gitlab.com/gx-regional/dakota/servus/v2/data
type IDrawdownDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*Drawdown, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Drawdown, error)

	// Save ...
	Save(ctx context.Context, newData *Drawdown) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*Drawdown) error

	// Update ...
	Update(ctx context.Context, newData *Drawdown) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *Drawdown, newData *Drawdown) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*Drawdown, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Drawdown, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *Drawdown) error
}

// DrawdownDAO ...
type DrawdownDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewDrawdownDAO creates a data access object for Drawdown
func NewDrawdownDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *DrawdownDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &DrawdownDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &Drawdown{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *DrawdownDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*Drawdown, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(drawdownDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(drawdownDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Drawdown), err
}

// LoadByIDOnSlave ...
func (dao *DrawdownDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Drawdown, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(drawdownDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(drawdownDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Drawdown), err
}

// Save ...
func (dao *DrawdownDAO) Save(ctx context.Context, entity *Drawdown) error {
	methodName := "save"
	defer dao.StatsD.Duration(drawdownDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(drawdownDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *DrawdownDAO) SaveBatch(ctx context.Context, entities []*Drawdown) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(drawdownDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(drawdownDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *DrawdownDAO) Update(ctx context.Context, data *Drawdown) error {
	methodName := "update"
	defer dao.StatsD.Duration(drawdownDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(drawdownDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *DrawdownDAO) UpdateEntity(ctx context.Context, preData *Drawdown, newData *Drawdown) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(drawdownDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(drawdownDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *DrawdownDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*Drawdown, error) {
	methodName := "find"
	defer dao.StatsD.Duration(drawdownDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(drawdownDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Drawdown, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Drawdown))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *DrawdownDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Drawdown, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(drawdownDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(drawdownDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Drawdown, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Drawdown))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *DrawdownDAO) Upsert(ctx context.Context, data *Drawdown) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(drawdownDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(drawdownDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
