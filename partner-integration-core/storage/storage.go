package storage

import (
	"gitlab.com/gx-regional/dakota/common/statsd"
	v2 "gitlab.com/gx-regional/dakota/servus/v2"
	"gitlab.com/gx-regional/dbmy/partner-integration/partner-integration-core/server/config"
)

// Init ...

func Init(app *v2.Application, conf *config.AppConfig, client statsd.Client) {
	app.MustRegister("storage.accountDAO", NewAccountDAO(conf.Data, client))
	app.MustRegister("storage.drawdownDAO", NewDrawdownDAO(conf.Data, client))
	app.MustRegister("storage.workflowexecutionmappingDAO", NewWorkflowExecutionMappingDAO(conf.Data, client))
}
