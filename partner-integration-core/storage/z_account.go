package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"strconv"
	"time"

	"gitlab.com/gx-regional/dakota/servus/v2"
	"gitlab.com/gx-regional/dakota/servus/v2/data"
	"gitlab.com/gx-regional/dakota/servus/v2/statsd"
)

var accountDao = "account_dao"

// GetID implements the GetID function for Entity Interface
func (impl *Account) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *Account) SetID(ID string) {
	// replace the logic to populate unique ID for Account
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *Account) NewEntity() data.Entity {
	return &Account{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *Account) GetTableName() string {
	return "account"
}

// IAccountDAO is the dao interface for Account
//
//go:generate mockery --name IAccountDAO --inpackage --case=underscore --replace-type gitlab.myteksi.net/gophers/go/commons/data=gitlab.com/gx-regional/dakota/servus/v2/data
type IAccountDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*Account, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Account, error)

	// Save ...
	Save(ctx context.Context, newData *Account) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*Account) error

	// Update ...
	Update(ctx context.Context, newData *Account) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *Account, newData *Account) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*Account, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Account, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *Account) error
}

// AccountDAO ...
type AccountDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewAccountDAO creates a data access object for Account
func NewAccountDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *AccountDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &AccountDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &Account{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *AccountDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*Account, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(accountDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(accountDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*Account), err
}

// LoadByIDOnSlave ...
func (dao *AccountDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Account, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(accountDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(accountDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*Account), err
}

// Save ...
func (dao *AccountDAO) Save(ctx context.Context, entity *Account) error {
	methodName := "save"
	defer dao.StatsD.Duration(accountDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(accountDao, methodName, "status: error")
	}
	return err
}

// SaveBatch ...
func (dao *AccountDAO) SaveBatch(ctx context.Context, entities []*Account) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(accountDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(accountDao, methodName, "status: error")
	}
	return err
}

// Update ...
func (dao *AccountDAO) Update(ctx context.Context, data *Account) error {
	methodName := "update"
	defer dao.StatsD.Duration(accountDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(accountDao, methodName, "status: error")
	}
	return err
}

// UpdateEntity ...
func (dao *AccountDAO) UpdateEntity(ctx context.Context, preData *Account, newData *Account) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(accountDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(accountDao, methodName, "status: error")
	}
	return err
}

// Find ...
func (dao *AccountDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*Account, error) {
	methodName := "find"
	defer dao.StatsD.Duration(accountDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(accountDao, methodName, "status: error")
	}
	foundObjects := make([]*Account, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Account))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *AccountDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Account, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(accountDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(accountDao, methodName, "status: error")
	}
	foundObjects := make([]*Account, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Account))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *AccountDAO) Upsert(ctx context.Context, data *Account) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(accountDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(accountDao, methodName, "status: error")
	}
	return err
}
