package storage

import "time"

// WorkflowExecutionMapping ...
type WorkflowExecutionMapping struct {
	ID        uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	RunID     string    `sql-col:"run_id" sql-key:"uk_run_id"`
	Domain    Domain    `sql-col:"domain" sql-key:"index_domain_domain_id"`
	DomainID  string    `sql-col:"domain_id" sql-key:"index_domain_domain_id"`
	CreatedAt time.Time `sql-col:"created_at" sql-key:"index_created_at" sql-insert:"false"`
	UpdatedAt time.Time `sql-col:"updated_at" sql-key:"index_updated_at" sql-insert:"false"`
}

type Domain string

const (
	Domain_ApplicationID Domain = "applicationID"
)

const (
	// nolint:unused
	logTagWorkflowExecutionMapping = "storage.workflowexecutionmapping"
	// nolint:unused
	ttlWorkflowExecutionMapping = 24 * 3600 // 1 day
)
