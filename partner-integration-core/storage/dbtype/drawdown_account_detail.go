package dbtype

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"reflect"
)

// DrawdownAccountDetail ...
type DrawdownAccountDetail struct {
	AccountNumber string `json:"accountNumber"`
	SwiftCode     string `json:"swiftCode"`
}

// Value implements driver.Value interface
func (d *DrawdownAccountDetail) Value() (driver.Value, error) {
	if d == nil {
		return nil, nil
	}

	return json.Marshal(d)
}

// Scan implements sql.Scanner interface
func (d *DrawdownAccountDetail) Scan(src interface{}) error {
	if src == nil || (reflect.ValueOf(src).Kind() == reflect.Ptr && reflect.ValueOf(src).IsNil()) {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return errors.New("not a byte array")
	}

	return json.Unmarshal(source, &d)
}
