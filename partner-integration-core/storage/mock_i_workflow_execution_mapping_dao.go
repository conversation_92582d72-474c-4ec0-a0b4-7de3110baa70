// Code generated by mockery v2.53.3. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.com/gx-regional/dakota/servus/v2/data"
)

// MockIWorkflowExecutionMappingDAO is an autogenerated mock type for the IWorkflowExecutionMappingDAO type
type MockIWorkflowExecutionMappingDAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockIWorkflowExecutionMappingDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*WorkflowExecutionMapping, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*WorkflowExecutionMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) ([]*WorkflowExecutionMapping, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*WorkflowExecutionMapping); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*WorkflowExecutionMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockIWorkflowExecutionMappingDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*WorkflowExecutionMapping, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindOnSlave")
	}

	var r0 []*WorkflowExecutionMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) ([]*WorkflowExecutionMapping, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*WorkflowExecutionMapping); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*WorkflowExecutionMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockIWorkflowExecutionMappingDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*WorkflowExecutionMapping, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for LoadByID")
	}

	var r0 *WorkflowExecutionMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) (*WorkflowExecutionMapping, error)); ok {
		return rf(ctx, ID, fields...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *WorkflowExecutionMapping); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*WorkflowExecutionMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockIWorkflowExecutionMappingDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*WorkflowExecutionMapping, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for LoadByIDOnSlave")
	}

	var r0 *WorkflowExecutionMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) (*WorkflowExecutionMapping, error)); ok {
		return rf(ctx, ID, fields...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *WorkflowExecutionMapping); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*WorkflowExecutionMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockIWorkflowExecutionMappingDAO) Save(ctx context.Context, newData *WorkflowExecutionMapping) error {
	ret := _m.Called(ctx, newData)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *WorkflowExecutionMapping) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockIWorkflowExecutionMappingDAO) SaveBatch(ctx context.Context, newData []*WorkflowExecutionMapping) error {
	ret := _m.Called(ctx, newData)

	if len(ret) == 0 {
		panic("no return value specified for SaveBatch")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*WorkflowExecutionMapping) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockIWorkflowExecutionMappingDAO) Update(ctx context.Context, newData *WorkflowExecutionMapping) error {
	ret := _m.Called(ctx, newData)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *WorkflowExecutionMapping) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockIWorkflowExecutionMappingDAO) UpdateEntity(ctx context.Context, preData *WorkflowExecutionMapping, newData *WorkflowExecutionMapping) error {
	ret := _m.Called(ctx, preData, newData)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEntity")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *WorkflowExecutionMapping, *WorkflowExecutionMapping) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockIWorkflowExecutionMappingDAO) Upsert(ctx context.Context, newData *WorkflowExecutionMapping) error {
	ret := _m.Called(ctx, newData)

	if len(ret) == 0 {
		panic("no return value specified for Upsert")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *WorkflowExecutionMapping) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMockIWorkflowExecutionMappingDAO creates a new instance of MockIWorkflowExecutionMappingDAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockIWorkflowExecutionMappingDAO(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockIWorkflowExecutionMappingDAO {
	mock := &MockIWorkflowExecutionMappingDAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
