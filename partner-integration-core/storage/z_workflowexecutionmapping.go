package storage

// Code generated .* DO NOT EDIT.

import (
    "context"
	"strconv"
    "time"

	"gitlab.com/gx-regional/dakota/servus/v2"
	"gitlab.com/gx-regional/dakota/servus/v2/data"
	"gitlab.com/gx-regional/dakota/servus/v2/statsd"
)

var workflow_execution_mappingDao = "workflow_execution_mapping_dao"

// GetID implements the GetID function for Entity Interface
func (impl *WorkflowExecutionMapping) GetID() string {
    // if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}


// SetID implements the SetID function for Entity Interface
func (impl *WorkflowExecutionMapping) SetID(ID string) {
    // replace the logic to populate unique ID for WorkflowExecutionMapping
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}


// NewEntity implements the NewEntity function for Entity Interface
func (impl *WorkflowExecutionMapping) NewEntity() data.Entity {
	return &WorkflowExecutionMapping {}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *WorkflowExecutionMapping) GetTableName() string {
	return "workflow_execution_mapping"
}

// IWorkflowExecutionMappingDAO is the dao interface for WorkflowExecutionMapping
//go:generate mockery --name IWorkflowExecutionMappingDAO --inpackage --case=underscore --replace-type gitlab.myteksi.net/gophers/go/commons/data=gitlab.com/gx-regional/dakota/servus/v2/data
type IWorkflowExecutionMappingDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*WorkflowExecutionMapping, error)

    // LoadByIDOnSlave ...
    LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*WorkflowExecutionMapping, error)

	// Save ...
	Save(ctx context.Context, newData *WorkflowExecutionMapping) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*WorkflowExecutionMapping) error

	// Update ...
	Update(ctx context.Context, newData *WorkflowExecutionMapping) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *WorkflowExecutionMapping, newData *WorkflowExecutionMapping) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*WorkflowExecutionMapping, error)

	// FindOnSlave ...
    FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*WorkflowExecutionMapping, error)

    // Upsert ...
    Upsert(ctx context.Context, newData *WorkflowExecutionMapping) error
}

// WorkflowExecutionMappingDAO ...
type WorkflowExecutionMappingDAO struct {
    data.DAO
    StatsD statsd.Client
}

// NewWorkflowExecutionMappingDAO creates a data access object for WorkflowExecutionMapping
func NewWorkflowExecutionMappingDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *WorkflowExecutionMappingDAO {
    if statsDClient == nil {
            statsDClient = statsd.NewNoop()
    }
    return &WorkflowExecutionMappingDAO{
		DAO: data.NewMysqlDAO(cfg.MySQL, &WorkflowExecutionMapping{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *WorkflowExecutionMappingDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*WorkflowExecutionMapping, error) {
    methodName := "load_by_id"
	defer dao.StatsD.Duration(workflow_execution_mappingDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
	    dao.StatsD.Count1(workflow_execution_mappingDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*WorkflowExecutionMapping), err
}

// LoadByIDOnSlave ...
func (dao *WorkflowExecutionMappingDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*WorkflowExecutionMapping, error) {
    methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(workflow_execution_mappingDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
	    dao.StatsD.Count1(workflow_execution_mappingDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*WorkflowExecutionMapping), err
}

// Save ...
func (dao *WorkflowExecutionMappingDAO) Save(ctx context.Context, entity *WorkflowExecutionMapping) error {
    methodName := "save"
	defer dao.StatsD.Duration(workflow_execution_mappingDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
    if err != nil {
        dao.StatsD.Count1(workflow_execution_mappingDao, methodName, "status: error")
    }
    return err
}

// SaveBatch ...
func (dao *WorkflowExecutionMappingDAO) SaveBatch(ctx context.Context, entities []*WorkflowExecutionMapping) error {
    methodName := "save_batch"
    defer dao.StatsD.Duration(workflow_execution_mappingDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
    if err != nil {
        dao.StatsD.Count1(workflow_execution_mappingDao, methodName, "status: error")
    }
    return err
}

// Update ...
func (dao *WorkflowExecutionMappingDAO) Update(ctx context.Context, data *WorkflowExecutionMapping) error {
	methodName := "update"
    defer dao.StatsD.Duration(workflow_execution_mappingDao, methodName, time.Now())
    err := dao.DAO.Update(ctx, data)
    if err != nil {
        dao.StatsD.Count1(workflow_execution_mappingDao, methodName, "status: error")
    }
    return err
}

// UpdateEntity ...
func (dao *WorkflowExecutionMappingDAO) UpdateEntity(ctx context.Context, preData *WorkflowExecutionMapping, newData *WorkflowExecutionMapping) error {
	methodName := "update_entity"
    defer dao.StatsD.Duration(workflow_execution_mappingDao, methodName, time.Now())
    err := dao.DAO.UpdateEntity(ctx, preData, newData)
    if err != nil {
        dao.StatsD.Count1(workflow_execution_mappingDao, methodName, "status: error")
    }
    return err
}

// Find ...
func (dao *WorkflowExecutionMappingDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*WorkflowExecutionMapping, error) {
    methodName := "find"
    defer dao.StatsD.Duration(workflow_execution_mappingDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
    if err != nil {
        dao.StatsD.Count1(workflow_execution_mappingDao, methodName, "status: error")
    }
	foundObjects := make([]*WorkflowExecutionMapping, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*WorkflowExecutionMapping))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *WorkflowExecutionMappingDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*WorkflowExecutionMapping, error) {
    methodName := "find_on_slave"
    defer dao.StatsD.Duration(workflow_execution_mappingDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
    if err != nil {
        dao.StatsD.Count1(workflow_execution_mappingDao, methodName, "status: error")
    }
	foundObjects := make([]*WorkflowExecutionMapping, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*WorkflowExecutionMapping))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *WorkflowExecutionMappingDAO) Upsert(ctx context.Context, data *WorkflowExecutionMapping) error {
	methodName := "upsert"
    defer dao.StatsD.Duration(workflow_execution_mappingDao, methodName, time.Now())
    err := dao.DAO.Upsert(ctx, data)
    if err != nil {
        dao.StatsD.Count1(workflow_execution_mappingDao, methodName, "status: error")
    }
    return err
}