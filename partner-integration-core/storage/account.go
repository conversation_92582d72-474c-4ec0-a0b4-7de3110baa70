package storage

import (
	"encoding/json"
	"time"
)

// Account ...
type Account struct {
	ID                 uint64          `sql-col:"id" sql-key:"id" sql-insert:"false"`
	AccountID          string          `sql-col:"account_id"`
	ParentAccountID    string          `sql-col:"parent_account_id"`
	CIF                string          `sql-col:"cif"`
	SafeID             string          `sql-col:"safe_id"`
	ProductVariantCode string          `sql-col:"product_variant_code"`
	PartnerID          string          `sql-col:"partner_id"`
	Metadata           json.RawMessage `sql-col:"metadata" sql-where:"false"`
	CreatedBy          string          `sql-col:"created_by"`
	UpdatedBy          string          `sql-col:"updated_by"`
	CreatedAt          time.Time       `sql-col:"created_at" sql-insert:"false"`
	UpdatedAt          time.Time       `sql-col:"updated_at" sql-insert:"false"`
}

const (
	AccountColAccountID = "AccountID"
)
